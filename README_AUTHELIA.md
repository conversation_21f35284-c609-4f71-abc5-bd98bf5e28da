# Authelia 身份验证集成指南

## 概述

本项目使用 Authelia 进行身份验证。Authelia 是一个开源的身份验证和授权服务器，提供单点登录(SSO)功能。

## 配置说明

### 环境变量

在部署应用时，需要设置以下环境变量来配置 Authelia 集成：

```
AUTHELIA_URL=https://your-authelia-instance.com
AUTHELIA_REDIRECT_URI=/auth/callback
AUTHELIA_LOGOUT_REDIRECT=/login
```

### Authelia 配置

在 Authelia 端，需要进行以下配置：

1. 确保 Authelia 已正确设置并运行
2. 配置 Authelia 的访问控制规则，允许对本应用的访问
3. 配置 Authelia 的 HTTP 头信息传递，确保以下头信息能够传递给应用：
   - `Remote-User` 或 `X-Forwarded-User`：用户名
   - `Remote-Email` 或 `X-Forwarded-Email`：用户邮箱
   - `Remote-Groups` 或 `X-Forwarded-Groups`：用户组（可选）

## 反向代理配置

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-app-domain.com;

    location / {
        proxy_pass http://localhost:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递 Authelia 头信息
        auth_request /authelia;
        auth_request_set $user $upstream_http_remote_user;
        auth_request_set $email $upstream_http_remote_email;
        auth_request_set $groups $upstream_http_remote_groups;
        
        proxy_set_header Remote-User $user;
        proxy_set_header Remote-Email $email;
        proxy_set_header Remote-Groups $groups;
    }

    location /authelia {
        internal;
        proxy_pass https://your-authelia-instance.com/api/verify;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URL $scheme://$host$request_uri;
    }
}
```

## 用户数据迁移

由于系统不再存储用户密码，原有的用户数据需要迁移：

1. 确保所有用户已在 Authelia 中创建账户
2. 用户首次通过 Authelia 登录后，系统会自动创建简化的用户记录

## 故障排除

如果遇到身份验证问题，请检查：

1. Authelia 服务是否正常运行
2. 环境变量是否正确配置
3. 反向代理是否正确传递 Authelia 头信息
4. 浏览器控制台是否有相关错误信息

## 安全注意事项

1. 确保 Authelia 和应用之间的通信是安全的
2. 定期检查 Authelia 的安全更新
3. 使用 HTTPS 保护所有通信