# Git
.git
.gitignore
.github

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# 日志文件
*.log

# 环境变量文件（通过volume挂载）
.env
.env.example

# 其他
.DS_Store
.idea/
.vscode/