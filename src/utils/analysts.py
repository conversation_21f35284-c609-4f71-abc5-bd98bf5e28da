"""与分析师配置相关的常量和实用工具。"""

from agents.aswath_damodaran import aswath_damodaran_agent
from agents.ben_graham import ben_graham_agent
from agents.bill_ackman import bill_ackman_agent
from agents.cathie_wood import cathie_wood_agent
from agents.charlie_munger import charlie_munger_agent
from agents.fundamentals import fundamentals_analyst_agent
from agents.michael_burry import michael_burry_agent
from agents.phil_fisher import phil_fisher_agent
from agents.peter_lynch import peter_lynch_agent
from agents.sentiment import sentiment_analyst_agent
from agents.stanley_druckenmiller import stanley_druckenmiller_agent
from agents.technicals import technical_analyst_agent
from agents.valuation import valuation_analyst_agent
from agents.warren_buffett import warren_buffett_agent
from agents.rakesh_jhunjhunwala import rakesh_jhunjhunwala_agent

# 定义分析师配置 - 单一真实来源
ANALYST_CONFIG = {
    "aswath_damodaran": {
        "display_name": "<PERSON>wath <PERSON>",
        "agent_func": aswath_damodaran_agent,
        "order": 0,
    },
    "ben_graham": {
        "display_name": "本·格雷厄姆",
        "agent_func": ben_graham_agent,
        "order": 1,
    },
    "bill_ackman": {
        "display_name": "比尔·阿克曼",
        "agent_func": bill_ackman_agent,
        "order": 2,
    },
    "cathie_wood": {
        "display_name": "凯茜·伍德",
        "agent_func": cathie_wood_agent,
        "order": 3,
    },
    "charlie_munger": {
        "display_name": "查理·芒格",
        "agent_func": charlie_munger_agent,
        "order": 4,
    },
    "michael_burry": {
        "display_name": "Michael Burry",
        "agent_func": michael_burry_agent,
        "order": 5,
    },
    "peter_lynch": {
        "display_name": "彼得·林奇",
        "agent_func": peter_lynch_agent,
        "order": 6,
    },
    "phil_fisher": {
        "display_name": "菲利普·费舍尔",
        "agent_func": phil_fisher_agent,
        "order": 7,
    },
    "rakesh_jhunjhunwala": {
        "display_name": "Rakesh Jhunjhunwala",
        "agent_func": rakesh_jhunjhunwala_agent,
        "order": 8,
    },
    "stanley_druckenmiller": {
        "display_name": "斯坦利·德鲁肯米勒",
        "agent_func": stanley_druckenmiller_agent,
        "order": 9,
    },
    "warren_buffett": {
        "display_name": "沃伦·巴菲特",
        "agent_func": warren_buffett_agent,
        "order": 10,
    },
    "technical_analyst": {
        "display_name": "技术分析师",
        "agent_func": technical_analyst_agent,
        "order": 11,
    },
    "fundamentals_analyst": {
        "display_name": "基本面分析师",
        "agent_func": fundamentals_analyst_agent,
        "order": 12,
    },
    "sentiment_analyst": {
        "display_name": "情绪分析师",
        "agent_func": sentiment_analyst_agent,
        "order": 13,
    },
    "valuation_analyst": {
        "display_name": "估值分析师",
        "agent_func": valuation_analyst_agent,
        "order": 14,
    },
}

# 从ANALYST_CONFIG派生ANALYST_ORDER以保持向后兼容性
ANALYST_ORDER = [(config["display_name"], key) for key, config in sorted(ANALYST_CONFIG.items(), key=lambda x: x[1]["order"])]

# 生成分析师名称映射
ANALYST_NAMES = {}
for key, config in ANALYST_CONFIG.items():
    ANALYST_NAMES[f"{key}_agent"] = config["display_name"]

# 添加额外的管理代理
ANALYST_NAMES.update({
    "portfolio_management_agent": "投资组合管理",
    "risk_management_agent": "风险管理"
})

def get_analyst_nodes():
    """获取分析师键到其(node_name, agent_func)元组的映射。"""
    return {key: (f"{key}_agent", config["agent_func"]) for key, config in ANALYST_CONFIG.items()}
