import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logger(name: str, log_file: str = None) -> logging.Logger:
    """配置并返回一个日志记录器。
    
    Args:
        name: 日志记录器的名称
        log_file: 日志文件路径，如果为None则只输出到控制台
        
    Returns:
        配置好的日志记录器实例
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 创建文件处理器，限制单个文件大小为10MB，最多保留5个备份
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

# 创建默认日志记录器
default_logger = setup_logger(
    'aifund',
    os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'run.log')
)

def get_logger(name: str = None) -> logging.Logger:
    """获取日志记录器。
    
    Args:
        name: 日志记录器的名称，如果为None则返回默认日志记录器
        
    Returns:
        日志记录器实例
    """
    if name is None:
        return default_logger
    return setup_logger(name)