import pytz
from datetime import datetime
import logging # 添加日志记录

# 配置一个简单的日志记录器 (如果项目中已有统一的日志配置，请遵循该配置)
logger = logging.getLogger(__name__)
 
# 定义标准时区 - 使用中国标准时间
STANDARD_TIMEZONE = pytz.timezone('Asia/Shanghai')

DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
DATE_FORMAT = '%Y-%m-%d'
TIME_FORMAT = '%H:%M:%S'

def normalize_date_string(date_input):
    """
    标准化日期字符串，提取日期部分（去除时间部分）
    
    Args:
        date_input: 日期输入，可能是字符串或datetime对象，支持格式：
                 - ISO格式 (2025-04-15T16:00:00Z)
                 - 标准格式 (2025-04-15 16:00:00)
                 - 纯日期格式 (2025-04-15)
                 - datetime对象
        
    Returns:
        标准化后的日期字符串（仅包含日期部分, 格式：YYYY-MM-DD）
    """
    if not date_input:
        return date_input
        
    try:
        # 如果是datetime对象，直接提取日期部分
        if isinstance(date_input, datetime):
            return date_input.strftime(DATE_FORMAT)
            
        # 尝试解析日期时间字符串
        if isinstance(date_input, str):
            # 处理包含 T 的 ISO 格式
            if 'T' in date_input:
                date_input = date_input.split('T')[0]
            # 处理包含空格和时间的标准格式
            elif ' ' in date_input:
                date_input = date_input.split(' ')[0]
            
        return date_input
            
    except Exception as e:
        logger.error(f"日期标准化失败: {date_input}, 错误: {str(e)}", exc_info=True)
        return date_input


def normalize_timestamp(timestamp_str):
    """
    将任意格式的时间戳标准化为统一格式，并确保时区一致
    
    Args:
        timestamp_str: 时间戳字符串，可能是ISO格式或其他格式
        
    Returns:
        标准化后的时间戳字符串，格式为 'YYYY-MM-DD HH:MM:SS'
    """
    try:
        # 处理ISO格式时间戳 (例如: 2025-03-12T08:00:00Z)
        if 'T' in timestamp_str:
            # 解析带时区的ISO格式
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        else:
            # 尝试解析其他格式，假设为本地时间
            try:
                dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M')
                except ValueError:
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d')
        
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        # 使用日志记录错误，但这里我们不直接导入logger以避免循环依赖
        logger.error(f"时间戳标准化失败: {timestamp_str}, 错误: {str(e)}", exc_info=True)
        return timestamp_str  # 如果处理失败，返回原始字符串

def convert_to_utc(date_str):
    """
    将本地日期字符串转换为UTC格式的日期字符串
    
    Args:
        date_str: 日期字符串，格式为 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS' 或 ISO格式 'YYYY-MM-DDThh:mm:ssZ'
        
    Returns:
        UTC格式的日期字符串
    """
    try:
        # 如果输入已经是datetime对象
        if isinstance(date_str, datetime):
            dt = date_str
            # 如果datetime对象没有时区信息，添加本地时区
            if dt.tzinfo is None:
                dt = STANDARD_TIMEZONE.localize(dt)
            # 转换为UTC
            utc_dt = dt.astimezone(pytz.UTC)
        # 处理ISO格式时间戳 (例如: 2025-03-12T08:00:00Z)
        elif isinstance(date_str, str) and 'T' in date_str:
            # 解析带时区的ISO格式
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            utc_dt = dt.astimezone(pytz.UTC)
        else:
            # 尝试解析不同格式的日期
            try:
                # 尝试解析带时间的格式
                dt = datetime.strptime(str(date_str), '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    dt = datetime.strptime(str(date_str), '%Y-%m-%d %H:%M')
                except ValueError:
                    # 如果只有日期，添加时间为当天开始
                    dt = datetime.strptime(str(date_str), '%Y-%m-%d')
            
            # 为本地时间添加时区信息（使用STANDARD_TIMEZONE）
            local_dt = STANDARD_TIMEZONE.localize(dt)
            # 转换为UTC
            utc_dt = local_dt.astimezone(pytz.UTC)
        
        # 根据原始格式返回相应的UTC格式
        if isinstance(date_str, str) and 'T' in date_str:
            # 如果是ISO格式，返回ISO格式
            return utc_dt.strftime('%Y-%m-%dT%H:%M:%SZ')
        elif isinstance(date_str, str) and ' ' in date_str:
            if ':' in date_str:
                # 如果原始字符串包含时间
                return utc_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return utc_dt.strftime('%Y-%m-%d')
        else:
            # 如果原始字符串只有日期或是datetime对象
            return utc_dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        logger.error(f"日期转换到UTC失败: {date_str}, 错误: {str(e)}", exc_info=True)
        return str(date_str)  # 如果处理失败，返回原始字符串


def is_us_market():
    """
    判断当前时间是否为美股市场时间
    默认返回True，因为主要交易美股
    
    Returns:
        bool: 是否为美股市场
    """
    return True

def get_market_timezone(market="US"):
    """
    获取指定市场的时区
    
    Args:
        market: 市场代码，默认为US（美股）
        
    Returns:
        pytz.timezone: 市场对应的时区对象
    """
    market_timezones = {
        "US": "America/New_York",  # 美股使用纽约时区
        "CN": "Asia/Shanghai",     # A股使用上海时区
        "HK": "Asia/Hong_Kong"     # 港股使用香港时区
    }
    
    timezone_str = market_timezones.get(market.upper(), "America/New_York")
    return pytz.timezone(timezone_str)

def convert_to_market_time(dt, market="US"):
    """
    将任意时间转换为指定市场的时间
    
    Args:
        dt: datetime对象或时间字符串
        market: 目标市场，默认为US
        
    Returns:
        datetime: 转换后的datetime对象（带时区信息）
    """
    if isinstance(dt, str):
        try:
            dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M')
            except ValueError:
                try:
                    dt = datetime.strptime(dt, '%Y-%m-%d')
                except ValueError:
                    return None
    
    # 如果输入时间没有时区信息，假定为UTC时间
    if dt.tzinfo is None:
        dt = pytz.UTC.localize(dt)
    
    # 转换到目标市场时区
    market_tz = get_market_timezone(market)
    return dt.astimezone(market_tz)

def is_market_open(market: str = "US") -> bool:
    """
    检查当前是否是交易时段
    
    Args:
        market: 市场类型，支持 "US"(美国) 和 "CN"(中国)，默认为 "US"
    
    Returns:
        bool: 市场是否开放
    """
    # 获取当前时间（标准时区）
    now_utc = datetime.now(pytz.utc)
    
    # 根据不同市场检查交易时段
    if market.upper() == "CN":
        market_tz = get_market_timezone("CN")
        now_market_time = now_utc.astimezone(market_tz)
        # 检查是否是工作日（中国时间）
        if now_market_time.weekday() >= 5:  # 5=周六, 6=周日
            return False
            
        # 中国市场（上午9:30-11:30，下午13:00-15:00）
        hour, minute = now_market_time.hour, now_market_time.minute
        
        # 上午交易时段
        if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
            return True
        
        # 下午交易时段
        if (hour == 13 or hour == 14): # 13:00 to 14:59:59
            return True
            
    elif market.upper() == "US":
        # 美国市场（9:30-16:00 EST/EDT）
        market_tz = get_market_timezone("US")
        us_eastern = now_utc.astimezone(market_tz)
        
        # 检查是否是工作日（美国东部时间）
        if us_eastern.weekday() >= 5:  # 5=周六, 6=周日
            return False
            
        hour, minute = us_eastern.hour, us_eastern.minute
        
        # 交易时段 9:30 AM - 4:00 PM
        if (hour == 9 and minute >= 30) or (hour > 9 and hour < 16):
            return True
    
    return False

def get_utc_date_for_api_start(local_date_str: str | None) -> str | None:
    """
    将本地日期字符串 (YYYY-MM-DD) 转换为 API 使用的 UTC 日期字符串 (YYYY-MM-DD)。
    转换后的 UTC 日期代表本地日期开始的那一天 (UTC)。
    假设 local_date_str 是 STANDARD_TIMEZONE (如 'Asia/Shanghai') 的日期。

    Args:
        local_date_str: 本地日期字符串 "YYYY-MM-DD"，或 None。

    Returns:
        对应的 UTC 日期字符串 "YYYY-MM-DD"，或 None。
    """
    if not local_date_str:
        return None
    try:
        # 本地日期的开始 (00:00:00)
        local_dt_start_of_day = datetime.strptime(local_date_str, DATE_FORMAT)
        aware_local_dt = STANDARD_TIMEZONE.localize(local_dt_start_of_day)
        utc_dt = aware_local_dt.astimezone(pytz.UTC)
        return utc_dt.strftime(DATE_FORMAT)
    except ValueError as e:
        logger.error(f"将本地开始日期 '{local_date_str}' 转换为 API UTC 日期失败: {e}", exc_info=True)
        raise ValueError(f"无效的本地开始日期格式: '{local_date_str}'. 期望格式 'YYYY-MM-DD'.") from e

def get_utc_date_for_api_end(local_date_str: str | None) -> str | None:
    """
    将本地日期字符串 (YYYY-MM-DD) 转换为 API 使用的 UTC 日期字符串 (YYYY-MM-DD)。
    转换后的 UTC 日期代表本地日期结束的那一天 (UTC)。
    假设 local_date_str 是 STANDARD_TIMEZONE (如 'Asia/Shanghai') 的日期。

    Args:
        local_date_str: 本地日期字符串 "YYYY-MM-DD"，或 None。

    Returns:
        对应的 UTC 日期字符串 "YYYY-MM-DD"，或 None。
    """
    if not local_date_str:
        return None # 对于 end_date, None 可能表示错误，调用者应检查
    try:
        # 本地日期的结束 (23:59:59)
        local_dt_end_of_day = datetime.strptime(local_date_str, DATE_FORMAT).replace(hour=23, minute=59, second=59, microsecond=999999)
        aware_local_dt = STANDARD_TIMEZONE.localize(local_dt_end_of_day)
        utc_dt = aware_local_dt.astimezone(pytz.UTC)
        return utc_dt.strftime(DATE_FORMAT)
    except ValueError as e:
        logger.error(f"将本地结束日期 '{local_date_str}' 转换为 API UTC 日期失败: {e}", exc_info=True)
        raise ValueError(f"无效的本地结束日期格式: '{local_date_str}'. 期望格式 'YYYY-MM-DD'.") from e