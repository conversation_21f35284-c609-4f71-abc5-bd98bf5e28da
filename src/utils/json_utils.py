import numpy as np
import json

def sanitize_json_data(data):
    """
    递归处理数据结构，将所有NaN和Infinity值替换为None，
    并处理特殊字符，确保数据可以被JSON序列化
    """
    if isinstance(data, dict):
        return {k: sanitize_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [sanitize_json_data(item) for item in data]
    elif isinstance(data, str):
        # 只处理换行符和回车符
        return data.replace('\n', '\\n').replace('\r', '\\r')
    elif isinstance(data, (float, np.float64, np.float32)):
        if np.isnan(data) or np.isinf(data):
            return None
        return float(data)
    else:
        return data

def json_serialize(data):
    """
    将数据序列化为JSON字符串，确保没有NaN或Infinity值
    """
    return json.dumps(sanitize_json_data(data), ensure_ascii=False)
