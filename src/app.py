import os
from flask import Flask
from werkzeug.middleware.proxy_fix import ProxyFix
from config import APP_CONFIG, AUTHELIA_CONFIG, STOCK_OPTIONS, STOCK_TO_MONITOR
from data.database import init_db
from auth.auth import init_auth, setup_oidc
from utils.logger import get_logger
from data.cache import get_cache

# Initialize Flask app
app = Flask(__name__)
app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1)

# Configure app
app.secret_key = APP_CONFIG['secret_key']
app.config.update({
    'SECRET_KEY': APP_CONFIG['secret_key'],
    'SESSION_COOKIE_SECURE': AUTHELIA_CONFIG['cookie_secure'],
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SAMESITE': 'Lax',  # 使用Lax而不是Strict，允许从外部链接带着会话进入
    'PREFERRED_URL_SCHEME': 'https',
    'SESSION_REFRESH_EACH_REQUEST': True,  # 确保每次请求都刷新会话
})

if AUTHELIA_CONFIG['cookie_domain']:
    app.config['SESSION_COOKIE_DOMAIN'] = AUTHELIA_CONFIG['cookie_domain']

# 初始化认证模块
login_manager = init_auth(app)


def init_app():
    """初始化应用"""
    logger = get_logger('web_app')
    
    # 确保目录存在
    os.makedirs(os.path.join(os.path.dirname(__file__), 'templates'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'static'), exist_ok=True)
    
    # 初始化数据库
    init_db()
    
    # 初始化OIDC
    with app.app_context():
        setup_oidc()
    
    # 初始化缓存
    cache = get_cache()
    logger.info("已初始化缓存系统")
    
    # 验证缓存是否正确加载
    prices_count = sum(len(data) for data in cache._prices_cache.values()) if cache._prices_cache else 0
    metrics_count = sum(len(data) for data in cache._financial_metrics_cache.values()) if cache._financial_metrics_cache else 0
    line_items_count = sum(len(data) for data in cache._line_items_cache.values()) if cache._line_items_cache else 0
    insider_trades_count = sum(len(data) for data in cache._insider_trades_cache.values()) if cache._insider_trades_cache else 0
    company_news_count = sum(len(data) for data in cache._company_news_cache.values()) if cache._company_news_cache else 0
    
    logger.info(f"缓存统计: 价格数据项 {prices_count}, 财务指标项 {metrics_count}, "
               f"财务项目项 {line_items_count}, 内部交易项 {insider_trades_count}, "
               f"公司新闻项 {company_news_count}")
    
    # Start task scheduler
    from task.task_service import start_scheduler
    logger.info("正在启动任务调度器...")
    start_scheduler(app)
    logger.info("任务调度器已启动")
    
    # Set up background data refresh
    # 启用后台数据刷新
    from task.task_service import schedule_background_refresh
    tickers_to_monitor = [code for code, _ in STOCK_TO_MONITOR]
    logger.info(f"正在设置后台数据刷新，监控股票: {tickers_to_monitor}")
    schedule_background_refresh(tickers_to_monitor)
    logger.info("后台数据刷新任务已设置")

# Import routes after app is created to avoid circular imports
from routes import register_routes
register_routes(app)

if __name__ == '__main__':
    init_app()
    app.run(debug=True, use_reloader=False, host='0.0.0.0', port=8888)  # Disable reloader to avoid duplicate scheduler starts
