import json
import sys
from io import StringIO
from data.database import SessionLocal, TradingRecord
from utils.json_utils import sanitize_json_data
from llm.models import get_model_info
from trading.trader import run_hedge_fund
from utils.logger import get_logger

def extract_decision_reasoning(decisions):
    """
    从决策数据中提取决策理由
    
    参数:
        decisions: 决策数据字典
    
    返回:
        包含决策理由的字典
    """
    reasoning_data = {}
    
    for ticker, decision in decisions.items():
        if isinstance(decision, dict) and 'reasoning' in decision:
            reasoning_data[ticker] = decision.get('reasoning')
    
    return reasoning_data

def run_trading_operation(
    tickers, 
    selected_analysts, 
    model_choice, 
    trading_date=None,
    initial_cash=100000.0, 
    margin_requirement=0.0, 
    show_reasoning=True,
    interval="minute", 
    interval_multiplier=1, 
    from_scheduled_task=False, 
    task_id=None,
    record_id=None
):
    """执行交易操作的核心逻辑"""
    logger = get_logger('trading')
    
    # 获取模型提供商
    model_info = get_model_info(model_choice)
    model_provider = model_info.provider.value if model_info else "未知"
    
    # 初始化投资组合
    portfolio = {
        "cash": initial_cash,
        "margin_requirement": margin_requirement,
        "margin_used": 0.0,
        "positions": {
            ticker: {
                "long": 0,
                "short": 0,
                "long_cost_basis": 0.0,
                "short_cost_basis": 0.0,
                "short_margin_used": 0.0,
            } for ticker in tickers
        },
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            } for ticker in tickers
        }
    }
    
    # 捕获输出
    old_stdout = sys.stdout
    mystdout = StringIO()
    sys.stdout = mystdout
    
    try:
        # 运行对冲基金
        result = run_hedge_fund(
            tickers=tickers,
            start_date=trading_date,
            end_date=trading_date,
            portfolio=portfolio,
            show_reasoning=show_reasoning,
            selected_analysts=selected_analysts,
            model_name=model_choice,
            model_provider=model_provider,
            interval=interval,
            interval_multiplier=interval_multiplier,
        )
        # 获取股票价格数据
        stock_prices = {}
        if 'stock_prices' in result:
            stock_prices = result['stock_prices']

        # 获取捕获的输出
        execution_log = mystdout.getvalue()
    finally:
        # 恢复标准输出
        sys.stdout = old_stdout
    
    # 处理执行日志中的Unicode转义序列
    try:
        if '\\u' in execution_log:
            execution_log = execution_log.encode().decode('unicode_escape')
        if not isinstance(execution_log, str):
            execution_log = execution_log.decode('utf-8')
    except Exception:
        pass
    
    # 保存到数据库
    db = SessionLocal()
    if record_id:
        # 更新现有记录
        record = db.query(TradingRecord).filter_by(id=record_id).first()
        if record:
            record.decisions = sanitize_json_data(result.get('decisions', {}))
            record.analyst_signals = sanitize_json_data(result.get('analyst_signals', {}))
            record.execution_log = execution_log
            record.interval = interval
            record.interval_multiplier = interval_multiplier
            record.metrics = sanitize_json_data({
                "stock_prices": stock_prices
            })
    else:
        # 创建新记录
        record = TradingRecord(
            type='trading',
            tickers=tickers,
            selected_analysts=selected_analysts,
            model_choice=model_choice,
            initial_cash=initial_cash,
            margin_requirement=margin_requirement,
            trading_date=trading_date,
            show_reasoning=show_reasoning,
            decisions=sanitize_json_data(result.get('decisions', {})),
            analyst_signals=sanitize_json_data(result.get('analyst_signals', {})),
            execution_log=execution_log,
            interval=interval,
            interval_multiplier=interval_multiplier,
            metrics=sanitize_json_data({
                "stock_prices": stock_prices
            })
        )
        db.add(record)
    
    db.commit()
    record_id = record.id
    db.close()
    
    # 如果是从任务调度器调用，返回结果ID
    if from_scheduled_task:
        print(f"定时任务 {task_id} 执行完成，记录ID: {record_id}")
        return {"record_id": record_id}
    
    # 否则返回原来的响应
    result_json = json.dumps({
        "decisions": result["decisions"],
        "analyst_signals": {k: {t: v[t] for t in tickers if t in v} 
                           for k, v in result["analyst_signals"].items()}
    }, ensure_ascii=False)
    
    return {
        "result": result,
        "tickers": tickers,
        "record_id": record_id,
        "result_json": result_json,
        "execution_log": execution_log
    }

def clean_backtest_data(data_list, data_type='trade'):
    """
    清理回测数据中的ANSI颜色代码并格式化数据
    
    参数:
        data_list: 要清理的数据列表
        data_type: 数据类型，'trade'表示交易历史，'summary'表示每日摘要
    
    返回:
        清理后的数据列表
    """
    import re
    
    if not data_list:
        return []
    
    cleaned_data = []
    for row in data_list:
        # 如果已经是字典格式，直接处理
        if isinstance(row, dict):
            cleaned_row = {k: re.sub(r'\x1b\[[0-9;]*m', '', v) if isinstance(v, str) else v 
                          for k, v in row.items()}
            cleaned_data.append(cleaned_row)
            continue
        
        # 处理列表格式
        if data_type == 'trade':
            # 交易历史数据格式
            cleaned_row = {
                'date': re.sub(r'\x1b\[[0-9;]*m', '', str(row[0])) if len(row) > 0 else '',
                'ticker': re.sub(r'\x1b\[[0-9;]*m', '', str(row[1])) if len(row) > 1 else '',
                'action': re.sub(r'\x1b\[[0-9;]*m', '', str(row[2])) if len(row) > 2 else '',
                'quantity': re.sub(r'\x1b\[[0-9;]*m', '', str(row[3])) if len(row) > 3 else 0,
                'price': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[4])).replace(',', '')) if len(row) > 4 and row[4] else 0,
                'shares_owned': re.sub(r'\x1b\[[0-9;]*m', '', str(row[5])) if len(row) > 5 else 0,
                'position_value': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[6])).replace('$', '').replace(',', '')) if len(row) > 6 and row[6] else 0,
                'bullish_count': int(re.sub(r'\x1b\[[0-9;]*m', '', str(row[7]))) if len(row) > 7 and row[7] else 0,
                'bearish_count': int(re.sub(r'\x1b\[[0-9;]*m', '', str(row[8]))) if len(row) > 8 and row[8] else 0,
                'neutral_count': int(re.sub(r'\x1b\[[0-9;]*m', '', str(row[9]))) if len(row) > 9 and row[9] else 0,
            }
        else:
            # 每日摘要数据格式
            is_summary = '投资组合摘要' in str(row[1]) if len(row) > 1 else False
            cleaned_row = {
                'date': re.sub(r'\x1b\[[0-9;]*m', '', str(row[0])) if len(row) > 0 else '',
                'is_summary': is_summary,
                'total_value': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[7])).replace('$', '').replace(',', '')) if len(row) > 7 and row[7] else 0,
                'cash_balance': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[6])).replace('$', '').replace(',', '')) if len(row) > 6 and row[6] else 0,
                'total_position_value': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[8])).replace('$', '').replace(',', '')) if len(row) > 8 and row[8] else 0,
                'return_pct': float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[9])).replace('+', '').replace('%', '')) if len(row) > 9 and row[9] else 0,
            }
            
            # 添加额外的性能指标
            if len(row) > 10:
                cleaned_row['sharpe_ratio'] = float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[10]))) if row[10] else 0
            if len(row) > 11:
                cleaned_row['sortino_ratio'] = float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[11]))) if row[11] else 0
            if len(row) > 12:
                cleaned_row['max_drawdown'] = float(re.sub(r'\x1b\[[0-9;]*m', '', str(row[12])).replace('%', '')) if row[12] else 0
        
        cleaned_data.append(cleaned_row)
    
    return cleaned_data
