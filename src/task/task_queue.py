from datetime import datetime, timedelta
import json
import traceback
from data.database import TradingRecord, SessionLocal, ScheduledTask, TaskExecution
from task.trading_service import run_trading_operation
from utils.logger import get_logger
from utils.time_utils import convert_to_market_time, is_us_market, is_market_open
from utils.time_utils import STANDARD_TIMEZONE

logger = get_logger('task_queue')

def get_all_tasks():
    """获取所有任务"""
    db = SessionLocal()
    try:
        tasks = db.query(ScheduledTask).all()
        return [task_to_dict(task) for task in tasks]
    finally:
        db.close()

def get_task_execution_history(limit=100):
    """获取任务执行历史"""
    db = SessionLocal()
    try:
        executions = db.query(TaskExecution).order_by(TaskExecution.execution_time.desc()).limit(limit).all()
        return [execution_to_dict(execution) for execution in executions]
    finally:
        db.close()

def get_task_stock_prices(task_id):
    """获取任务相关的股票价格数据"""
    from utils.logger import get_logger
    from data.cache import get_cache
    logger = get_logger('task_queue')
    
    db = SessionLocal()
    try:
        # 获取任务信息
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if not task:
            logger.warning(f"任务ID {task_id} 不存在")
            return {}
            
        # 获取执行记录并确定时间范围
        executions = db.query(TaskExecution).filter_by(task_id=task_id).order_by(TaskExecution.execution_time).all()
        if not executions:
            logger.warning(f"任务 {task_id} 没有执行记录")
            return {}
            
        # 将时间转换为标准时区（中国时间）
        start_time = STANDARD_TIMEZONE.localize(executions[0].execution_time.replace(microsecond=0))
        end_time = STANDARD_TIMEZONE.localize(executions[-1].execution_time.replace(microsecond=0))
        
        # 获取缓存实例
        cache = get_cache()
        
        # 获取所有相关记录的股票价格数据
        stock_prices = {}
        for ticker in task.tickers.split(','):
            # 从缓存获取数据
            cached_prices = cache.get_prices(ticker, interval="minute", interval_multiplier=1)
            if cached_prices:
                stock_prices[ticker] = []
                for price_data in cached_prices:
                    if isinstance(price_data, dict):
                        try:
                            # 尝试解析时间字符串
                            try:
                                price_time = datetime.strptime(price_data['time'], '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                price_time = datetime.strptime(price_data['time'], '%Y-%m-%d %H:%M')
                            
                            # 将时间转换为中国时区
                            china_time = convert_to_market_time(price_time, market='CN')
                            if china_time:
                                # 检查时间是否在执行时间范围内
                                if start_time <= china_time <= end_time:
                                    data = {
                                        'date': china_time.strftime('%Y-%m-%d %H:%M:%S'),
                                        'price': price_data['close']
                                    }
                                    stock_prices[ticker].append(data)
                        except Exception as e:
                            logger.error(f"时间格式转换失败: {str(e)}")
            else:
                stock_prices[ticker] = []
                logger.info(f"缓存中没有找到股票 {ticker} 的数据")
        
        # 对每只股票的价格数据按日期排序
        for ticker in stock_prices:
            stock_prices[ticker] = sorted(stock_prices[ticker], key=lambda x: x['date'])
            
        return stock_prices
    except Exception as e:
        logger.error(f"获取股票价格数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {}
    finally:
        db.close()

def get_task_by_id(task_id):
    """根据ID获取任务"""
    db = SessionLocal()
    try:
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if task:
            return task_to_dict(task)
        return None
    finally:
        db.close()

def pause_task_by_id(task_id):
    """暂停任务"""
    db = SessionLocal()
    try:
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if not task:
            return False, "任务不存在"
        
        task.status = 'paused'
        db.commit()
        return True, "任务已暂停"
    except Exception as e:
        db.rollback()
        return False, f"暂停任务失败: {str(e)}"
    finally:
        db.close()

def resume_task_by_id(task_id):
    """恢复任务"""
    db = SessionLocal()
    try:
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if not task:
            return False, "任务不存在"
        
        if task.status != 'paused':
            return False, "只能恢复已暂停的任务"
        
        task.status = 'active'
        # 更新下次执行时间
        task.next_run = calculate_next_run_time(task.execution_interval)
        db.commit()
        return True, "任务已恢复"
    except Exception as e:
        db.rollback()
        return False, f"恢复任务失败: {str(e)}"
    finally:
        db.close()

def delete_task_by_id(task_id):
    """删除任务"""
    db = SessionLocal()
    try:
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if not task:
            return False, "任务不存在"
        
        db.delete(task)
        db.commit()
        return True, "任务已删除"
    except Exception as e:
        db.rollback()
        return False, f"删除任务失败: {str(e)}"
    finally:
        db.close()

def add_task(task_data):
    """添加新任务"""
    db = SessionLocal()
    try:
        # 计算下次执行时间
        next_run = calculate_next_run_time(task_data['execution_interval'])
        
        # 创建任务
        task = ScheduledTask(
            name=task_data.get('task_name'),
            tickers=task_data['tickers'],
            execution_interval=task_data['execution_interval'],
            next_run=next_run,
            end_condition=task_data['end_condition'],
            end_count=task_data.get('end_count'),
            end_date=task_data.get('end_date'),
            selected_analysts=task_data['selected_analysts'],
            model_choice=task_data['model_choice'],
            initial_cash=float(task_data.get('initial_cash', 100000.0)),
            margin_requirement=float(task_data.get('margin_requirement', 0.0)),
            interval=task_data.get('interval', 'minute'),
            show_reasoning=task_data.get('show_reasoning', True),
            task_data=task_data,
            execution_time_start=task_data.get('execution_time_start'),
            execution_time_end=task_data.get('execution_time_end')
        )
        
        db.add(task)
        db.commit()
        return True, task.id
    except Exception as e:
        db.rollback()
        return False, f"添加任务失败: {str(e)}"
    finally:
        db.close()

def record_task_execution(task_id, status='success', record_id=None, error_message=None):
    """记录任务执行"""
    db = SessionLocal()
    try:
        task = db.query(ScheduledTask).filter_by(id=task_id).first()
        if not task:
            return False
        
        # 更新任务状态
        task.last_run = datetime.now()
        task.execution_count += 1
        task.task_data = task.task_data or {}
        task.task_data['last_execution_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 检查是否达到结束条件
        if task.end_condition == 'count' and task.execution_count >= task.end_count:
            task.status = 'completed'
        elif task.end_condition == 'date' and datetime.now() >= task.end_date:
            task.status = 'completed'
        else:
            # 计算下次执行时间
            task.next_run = calculate_next_run_time(task.execution_interval)
        
        # 记录执行历史
        execution = TaskExecution(
            task_id=task_id,
            task_name=task.name,
            status=status,
            record_id=record_id,
            error_message=error_message
        )
        
        db.add(execution)
        db.commit()
        return True
    finally:
        db.close()

def calculate_next_run_time(interval):
    """根据执行间隔计算下次执行时间"""
    now = datetime.now()
    
    if interval == '1m':
        return now + timedelta(minutes=1)
    elif interval == '3m':
        return now + timedelta(minutes=3)
    elif interval == '5m':
        return now + timedelta(minutes=5)
    elif interval == '15m':
        return now + timedelta(minutes=15)
    elif interval == '30m':
        return now + timedelta(minutes=30)
    elif interval == '1h':
        return now + timedelta(hours=1)
    elif interval == '4h':
        return now + timedelta(hours=4)
    elif interval == '1d':
        return now + timedelta(days=1)
    else:
        # 默认1小时
        return now + timedelta(hours=1)

def task_to_dict(task):
    """将任务对象转换为字典"""
    return {
        'id': task.id,
        'name': task.name,
        'tickers': task.tickers,
        'execution_interval': task.execution_interval,
        'created_at': task.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'next_run': task.next_run.strftime('%Y-%m-%d %H:%M:%S'),
        'last_run': task.last_run.strftime('%Y-%m-%d %H:%M:%S') if task.last_run else None,
        'status': task.status,
        'execution_count': task.execution_count,
        'end_condition': task.end_condition,
        'end_count': task.end_count,
        'end_date': task.end_date.strftime('%Y-%m-%d') if task.end_date else None,
        'selected_analysts': task.selected_analysts,
        'model_choice': task.model_choice,
        'initial_cash': task.initial_cash,
        'margin_requirement': task.margin_requirement,
        'interval': task.interval,
        'interval_multiplier': task.interval_multiplier,
        'show_reasoning': task.show_reasoning,
        'analysts': task.selected_analysts,  # 为前端显示准备
        'model': task.model_choice,  # 为前端显示准备
        'execution_time_start': task.execution_time_start,
        'execution_time_end': task.execution_time_end
    }

def execution_to_dict(execution):
    """将执行历史对象转换为字典"""
    db = SessionLocal()
    try:
        result = {
            'id': execution.id,
            'task_id': execution.task_id,
            'task_name': execution.task_name,
            'execution_time': execution.execution_time.strftime('%Y-%m-%d %H:%M:%S'),
            'status': execution.status,
            'record_id': execution.record_id,
            'error_message': execution.error_message,
            'decisions': {},
            'analyst_signals': {}
        }
        
        # 如果有关联的交易记录，获取决策数据
        if execution.record_id:
            from data.database import TradingRecord
            trading_record = db.query(TradingRecord).filter_by(id=execution.record_id).first()
            if trading_record:
                result['decisions'] = trading_record.decisions or {}
                result['analyst_signals'] = trading_record.analyst_signals or {}
        
        return result
    finally:
        db.close()

def get_due_tasks():
    """获取到期需要执行的任务"""
    db = SessionLocal()
    try:
        now = datetime.now()
        
        # 获取到期的任务
        tasks = db.query(ScheduledTask).filter(
            ScheduledTask.status == 'active',
            ScheduledTask.next_run <= now
        ).all()
        
        return [task_to_dict(task) for task in tasks]
    finally:
        db.close()

def is_within_execution_window(task_dict):
    """检查当前时间是否在任务的执行时段内 (使用 task_dict)"""
    start_str = task_dict.get('execution_time_start')
    end_str = task_dict.get('execution_time_end')

    logger.info(f"检查任务 {task_dict['id']} 的执行时段: {start_str} - {end_str}")
    
    # 如果是美股市场且市场未开放，跳过执行
    if is_us_market() and not is_market_open("US"):
        logger.info("美股市场未开放，跳过执行")
        return False
    
    if not start_str or not end_str:
        logger.debug("未设置执行时段，允许执行")
        return True  # 如果没有设置执行时段，则始终允许执行
        
    try:
        # 验证时间格式
        if not all(len(t.split(':')) == 2 for t in [start_str, end_str]):
            logger.warning(f"时间格式错误: {start_str} 或 {end_str}，应为 HH:MM 格式")
            return True
            
        # 获取当前时间并确保使用正确的时区
        current_time = datetime.now().time()
        
        # 解析时间字符串并设置相同的时区
        start_time_dt = datetime.strptime(start_str, '%H:%M')
        end_time_dt = datetime.strptime(end_str, '%H:%M')
        
        # 提取时间部分
        start_time = start_time_dt.time()
        end_time = end_time_dt.time()
        
        # 处理跨日的情况（例如 21:30-04:30）
        if start_time > end_time:
            result = current_time >= start_time or current_time <= end_time
            logger.debug(f"跨日时段判断: {current_time} 是否在 {start_time} - {end_time} 之间: {result}")
            return result
        else:
            result = start_time <= current_time <= end_time
            logger.debug(f"当日时段判断: {current_time} 是否在 {start_time} - {end_time} 之间: {result}")
            return result
    except ValueError as e:
        logger.warning(f"时间格式解析错误: {str(e)}，忽略时间限制")
        return True

def execute_due_tasks(run_trading_func):
    """执行所有到期的任务"""
    from utils.logger import get_logger
    logger = get_logger('task_queue')
    due_tasks = get_due_tasks() # get_due_tasks 返回字典列表
    
    for task_dict in due_tasks:
        try:
            # 检查是否在执行时段内
            if not is_within_execution_window(task_dict):
                logger.info(f"任务 {task_dict['name'] or task_dict['id']} (ID: {task_dict['id']}) 跳过，当前时间不在执行时段内 ({task_dict.get('execution_time_start')} - {task_dict.get('execution_time_end')})")
                
                # 更新任务的下次执行时间
                db = SessionLocal()
                try:
                    task = db.query(ScheduledTask).filter_by(id=task_dict['id']).first()
                    if task:
                        # 计算下一个执行时间
                        task.next_run = calculate_next_run_time(task.execution_interval)
                        # logger.info(f"更新任务 {task_dict['id']} 的下次执行时间为: {task.next_run}")
                        db.commit()
                except Exception as e:
                    logger.error(f"更新任务下次执行时间失败: {str(e)}")
                    db.rollback()
                finally:
                    db.close()

                continue
            
            # logger.info(f"执行任务: {task_dict['name'] or task_dict['id']} (ID: {task_dict['id']})")
            
            # 执行交易
            result = run_trading_operation(
                tickers=task_dict['tickers'].split(','),
                selected_analysts=task_dict['selected_analysts'],
                model_choice=task_dict['model_choice'],
                trading_date=datetime.now(),
                initial_cash=task_dict['initial_cash'],
                margin_requirement=task_dict['margin_requirement'],
                show_reasoning=task_dict['show_reasoning'],
                interval=task_dict['interval'],
                interval_multiplier=task_dict.get('interval_multiplier', 1),
                from_scheduled_task=True,
                task_id=task_dict['id']
            )
            
            # 从结果中正确提取 record_id
            record_id = result.get('record_id') if isinstance(result, dict) else result
            
            # 记录执行结果
            success = record_task_execution(
                task_id=task_dict['id'],
                status='success',
                record_id=record_id
            )
            
            # logger.info(f"任务 {task_dict['id']} 执行完成，记录ID: {record_id}")
            
        except Exception as e:
            logger.error(f"执行任务 {task_dict.get('id', 'N/A')} 时失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 记录执行失败
            record_task_execution(
                task_id=task_dict['id'],
                status='failed',
                error_message=str(e)
            )

def get_task_executions(task_id, days=None):
    """
    获取任务的执行历史
    
    Args:
        task_id: 任务ID
        days: 获取最近几天的数据，None表示获取所有数据
    """
    db = SessionLocal()
    try:
        query = db.query(TaskExecution).filter_by(task_id=task_id)
        
        # 如果指定了天数，添加时间范围过滤
        if days is not None:
            start_date = datetime.now() - timedelta(days=days)
            query = query.filter(TaskExecution.execution_time >= start_date)
        
        # 按执行时间降序排序
        executions = query.order_by(TaskExecution.execution_time.desc()).all()
        
        # 转换为字典列表，以便在模板中更容易处理
        result = []
        for execution in executions:
            execution_dict = {
                'id': execution.id,
                'task_id': execution.task_id,
                'execution_time': execution.execution_time.strftime('%Y-%m-%d %H:%M:%S'),
                'status': execution.status,
                'record_id': execution.record_id,
                'decisions': {},
                'analyst_signals': {}
            }
            
            # 如果有关联的交易记录，获取决策信息和分析师信号
            if execution.record_id:
                record = db.query(TradingRecord).filter_by(id=execution.record_id).first() 
                if record:
                    # 获取决策信息
                    if record.decisions:
                        try:
                            if isinstance(record.decisions, str):
                                execution_dict['decisions'] = json.loads(record.decisions)
                            else:
                                execution_dict['decisions'] = record.decisions
                        except:
                            pass
                    
                    # 获取分析师信号
                    if record.analyst_signals:
                        try:
                            analyst_signals = record.analyst_signals
                            if isinstance(analyst_signals, str):
                                analyst_signals = json.loads(analyst_signals)
                            
                            # 创建过滤后的分析师信号
                            filtered_signals = {}
                            for analyst, stocks in analyst_signals.items():
                                filtered_signals[analyst] = {}
                                for ticker, signal_data in stocks.items():
                                    filtered_signals[analyst][ticker] = {
                                        'signal': signal_data.get('signal'),
                                        'confidence': signal_data.get('confidence')
                                    }
                            
                            execution_dict['analyst_signals'] = filtered_signals
                        except Exception as e:
                            logger.error(f"处理分析师信号时出错: {str(e)}")
                            execution_dict['analyst_signals'] = {}
            
            result.append(execution_dict)
        
        return result
    finally:
        db.close()
