import sys
import traceback
from io import StringIO
from trading.trader import run_hedge_fund, create_workflow
from trading.backtester import Backtester
from data.database import SessionLocal, TradingRecord
from utils.json_utils import sanitize_json_data
from task.trading_service import clean_backtest_data
from utils.logger import get_logger


def run_backtest_task(record_id, tickers, selected_analysts, model_choice, model_provider,
                     start_date, end_date, initial_capital, margin_requirement, show_reasoning, force_refresh):
    """
    Execute backtest task in background thread
    
    Args:
        record_id: Database record ID
        tickers: List of stock tickers
        selected_analysts: List of selected analysts
        model_choice: LLM model to use
        model_provider: Provider of the LLM model
        start_date: Backtest start date
        end_date: Backtest end date
        initial_capital: Initial capital amount
        margin_requirement: Margin requirement percentage
        show_reasoning: Whether to show reasoning
    """
    logger = get_logger('backtest_task')
    
    # 初始化数据库记录状态
    db = SessionLocal()
    try:
        record = db.query(TradingRecord).filter_by(id=record_id).first()
        if record:
            record.metrics = sanitize_json_data({
                'status': 'running',
                'progress': 0,
                'error': None
            })
            db.commit()
    finally:
        db.close()
    
    # 创建工作流
    try:
        workflow = create_workflow(selected_analysts)
        agent = workflow.compile()
        
        # 捕获输出
        old_stdout = sys.stdout
        mystdout = StringIO()
        sys.stdout = mystdout
        
        try:
            # 创建回测器
            backtester = Backtester(
                agent=run_hedge_fund,
                tickers=tickers,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                model_name=model_choice,
                model_provider=model_provider,
                selected_analysts=selected_analysts,
                initial_margin_requirement=margin_requirement,
                show_reasoning=show_reasoning,
                force_refresh=force_refresh,
            )
            
            # 运行回测
            performance_metrics = backtester.run_backtest()
            
            # 获取输出
            execution_log = mystdout.getvalue()
            
        finally:
            # 恢复标准输出
            sys.stdout = old_stdout
        
        # 处理结果
        metrics_json = sanitize_json_data({
            'status': performance_metrics.get('status', 'completed'),
            'progress': performance_metrics.get('progress', 100),
            'error': performance_metrics.get('error'),
            'sharpe_ratio': float(performance_metrics['sharpe_ratio']) if performance_metrics.get('sharpe_ratio') is not None else None,
            'sortino_ratio': float(performance_metrics['sortino_ratio']) if performance_metrics.get('sortino_ratio') is not None else None,
            'max_drawdown': float(performance_metrics['max_drawdown']) if performance_metrics.get('max_drawdown') is not None else None,
            'long_short_ratio': float(performance_metrics.get('long_short_ratio', 0)) if performance_metrics.get('long_short_ratio') is not None else None,
            'gross_exposure': float(performance_metrics.get('gross_exposure', 0)) if performance_metrics.get('gross_exposure') is not None else None,
            'net_exposure': float(performance_metrics.get('net_exposure', 0)) if performance_metrics.get('net_exposure') is not None else None,
            'total_return': float(performance_metrics.get('total_return', 0)) if performance_metrics.get('total_return') is not None else None,
            'stock_prices': performance_metrics.get('stock_prices', {}),
            'daily_decisions': performance_metrics.get('daily_decisions', {}),
            'daily_analyst_signals': performance_metrics.get('daily_analyst_signals', {})
        })
        
        # 处理投资组合价值历史
        portfolio_values = sanitize_json_data([{
            'date': pv['Date'].strftime('%Y-%m-%d'),
            'value': float(pv['Portfolio Value'])
        } for pv in backtester.portfolio_values])
        
        # 处理交易历史和每日摘要
        trade_history = performance_metrics.get('trade_history', [])
        daily_summaries = performance_metrics.get('daily_summaries', [])
        
        cleaned_trade_history = clean_backtest_data(trade_history, 'trade')
        cleaned_daily_summaries = clean_backtest_data(daily_summaries, 'summary')
        
        daily_decisions = performance_metrics.get('daily_decisions', {})
        daily_analyst_signals = performance_metrics.get('daily_analyst_signals', {})

        logger.info(f"处理了 {len(cleaned_trade_history)} 条交易记录和 {len(cleaned_daily_summaries)} 条每日摘要")
        
        # 处理执行日志的Unicode转义序列
        try:
            if '\\u' in execution_log:
                execution_log = execution_log.encode().decode('unicode_escape')
            if not isinstance(execution_log, str):
                execution_log = execution_log.decode('utf-8')
        except Exception as e:
            logger.error(f"处理执行日志时出错: {e}")
        
        # 更新数据库记录
        db = SessionLocal()
        record = db.query(TradingRecord).filter_by(id=record_id).first()
        if record:
            record.execution_log = execution_log
            record.metrics = metrics_json
            record.portfolio_values = portfolio_values
            record.trade_history = sanitize_json_data(cleaned_trade_history)
            record.daily_summaries = sanitize_json_data(cleaned_daily_summaries)
            # 保存每日决策和分析师信号
            record.daily_decisions = sanitize_json_data(daily_decisions)
            record.daily_analyst_signals = sanitize_json_data(daily_analyst_signals)
            db.commit()
        db.close()
        
        logger.info(f"回测任务 {record_id} 完成")
        
    except Exception as e:
        logger.error(f"回测任务 {record_id} 失败: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 更新数据库记录为失败状态
        db = SessionLocal()
        record = db.query(TradingRecord).filter_by(id=record_id).first()
        if record:
            record.execution_log = f"回测执行失败: {str(e)}\n{traceback.format_exc()}"
            record.metrics = sanitize_json_data({
                "status": "failed",
                "progress": 0,
                "error": str(e)
            })
            db.commit()
        db.close()
