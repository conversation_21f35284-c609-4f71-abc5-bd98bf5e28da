import threading
import time
import traceback
import pytz
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime, timedelta
from task.task_queue import execute_due_tasks, get_due_tasks
from utils.logger import get_logger
from task.trading_service import run_trading_operation
from tools.api import get_prices, get_financial_metrics, search_line_items, get_insider_trades, get_company_news
from utils.time_utils import is_market_open

# Global flag for background scheduler
_background_scheduler_started = False

# 获取日志记录器
logger = get_logger(__name__)

def task_scheduler():
    """Task scheduler thread, periodically checks and executes due tasks"""
    logger = get_logger('task_scheduler')
    logger.info("Task scheduler thread started...")
    while True:
        try:
            # logger.info("Checking due tasks...")
            execute_due_tasks(run_trading_operation)
        except Exception as e:
            logger.error(f"Task scheduler error: {str(e)}")
            logger.error(traceback.format_exc())
        
        # Check every 30 seconds
        time.sleep(30)

def start_scheduler(app):
    """Start scheduler thread"""
    logger = get_logger('scheduler')
    try:
        # Ensure only one scheduler thread is started
        if not hasattr(app, '_scheduler_started'):
            scheduler_thread = threading.Thread(
                target=task_scheduler,
                daemon=True,
                name='TaskScheduler'
            )
            scheduler_thread.start()
            app._scheduler_started = True
            logger.info("Task scheduler successfully started")
    except Exception as e:
        logger.error(f"Failed to start scheduler: {str(e)}")
        logger.error(traceback.format_exc())

def schedule_background_refresh(tickers: list[str], market: str = "US"):
    """Set up background periodic refresh tasks"""
    global _background_scheduler_started
    
    # If scheduler already started, don't start again
    if _background_scheduler_started:
        return None
    
    logger = get_logger('background_refresh')
    
    logger.info(f"设置定时数据刷新任务，监控股票: {tickers}, 市场: {market}")
    
    # Create scheduler
    scheduler = BackgroundScheduler()
    
    # Refresh financial data every day at 6 AM
    if market.upper() == "US":
        preload_timezone = pytz.timezone('US/Eastern')
        preload_hour = 6  # 美国东部时间6点（开市前）
    else:  # CN
        preload_timezone = pytz.timezone('Asia/Shanghai')
        preload_hour = 8  # 中国时间8点（开市前）
    
    # 每日刷新财务数据
    scheduler.add_job(
        lambda: preload_daily_data(tickers, force_refresh=True),
        'cron',
        hour=preload_hour,
        minute=0,
        id=f'daily_financial_refresh_{market}',
        timezone=preload_timezone
    )
    
    # 市场开放时每5分钟刷新价格数据
    def refresh_price_data_if_market_open():
        if is_market_open(market):
            logger.info(f"{market} 市场开放中，刷新价格数据...")
            for ticker in tickers:
                try:
                    # 使用标准时区获取当前时间
                    now = datetime.now()
                    end_date = now.strftime('%Y-%m-%d %H:%M:%S')
                    # 刷新分钟级数据
                    get_prices(ticker, end_date, end_date, interval="minute", interval_multiplier=1, force_refresh=True)
                    logger.debug(f"已刷新 {ticker} 的分钟级价格数据")
                    
                    # 同时刷新日级数据
                    start_date_daily = (now - timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')
                    get_prices(ticker, start_date_daily, end_date, interval="day", interval_multiplier=1, force_refresh=True)
                    logger.debug(f"已刷新 {ticker} 的日级价格数据")
                except Exception as e:
                    logger.error(f"刷新 {ticker} 价格数据失败: {str(e)}")
        else:
            logger.debug(f"{market} 市场已关闭，跳过价格数据刷新")
    
    # 设置刷新频率
    refresh_interval = 5  # 默认5分钟
    
    scheduler.add_job(
        refresh_price_data_if_market_open,
        'interval',
        minutes=refresh_interval,
        id=f'price_refresh_{market}'
    )
    
    # 每4小时刷新一次新闻和内部交易数据
    def refresh_news_and_insider_trades():
        logger.info(f"定时刷新新闻和内部交易数据...")
        now = datetime.now()
        end_date = now.strftime('%Y-%m-%d')
        start_date = (now - timedelta(days=365)).strftime('%Y-%m-%d')
        
        for ticker in tickers:
            try:
                # 刷新新闻数据
                get_company_news(ticker, end_date, start_date, force_refresh=True)
                logger.debug(f"已刷新 {ticker} 的新闻数据")
                
                # 刷新内部交易数据
                get_insider_trades(ticker, end_date, start_date, force_refresh=True)
                logger.debug(f"已刷新 {ticker} 的内部交易数据")
            except Exception as e:
                logger.error(f"刷新 {ticker} 新闻和内部交易数据失败: {str(e)}")
    
    scheduler.add_job(
        refresh_news_and_insider_trades,
        'interval',
        hours=4,
        id=f'news_insider_refresh_{market}'
    )
    
    # Start scheduler
    try:
        scheduler.start()
        logger.info(f"已启动 {market} 市场的定时数据刷新任务")
        
        # Mark scheduler as started
        _background_scheduler_started = True
        
        return scheduler
    except Exception as e:
        logger.error(f"启动定时任务失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def preload_daily_data(tickers: list[str], end_date: str = None, force_refresh: bool = False):
    """
    每日一次性预加载多个股票的基础数据
    
    Args:
        tickers: 股票代码列表
        end_date: 结束日期，默认为当前日期
        force_refresh: 是否强制刷新，忽略缓存
    """
    from config import NO_KEY_REQUIRED_TICKERS
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 使用标准时区获取当前时间
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 计算开始日期
    start_date = (datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S') - timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')
    logger.info(f"预加载 {tickers} 的基础数据，开始日期: {start_date}, 结束日期: {end_date}")
    
    # 优先处理不需要API密钥的股票
    prioritized_tickers = sorted(tickers, key=lambda t: t not in NO_KEY_REQUIRED_TICKERS)
    
    for ticker in prioritized_tickers:
        try:
            # 加载财务指标
            get_financial_metrics(ticker, end_date, force_refresh=force_refresh)
            
            # 加载常用财务项目
            common_items = [
                "book_value_per_share",                      # 每股账面价值
                "capital_expenditure",                       # 资本支出
                "cash_and_equivalents",                      # 现金及等价物
                "current_assets",                            # 流动资产
                "current_liabilities",                       # 流动负债
                "debt_to_equity",                           # 资产负债率
                "depreciation_and_amortization",            # 折旧和摊销
                "dividends_and_other_cash_distributions",   # 股息和其他现金分配
                "earnings_per_share",                       # 每股收益
                "ebit",                                     # 息税前利润
                "ebitda",                                   # 息税折旧摊销前利润
                "free_cash_flow",                          # 自由现金流
                "goodwill_and_intangible_assets",          # 商誉和无形资产
                "gross_margin",                            # 毛利率
                "issuance_or_purchase_of_equity_shares",   # 股票发行或回购
                "net_income",                              # 净利润
                "operating_expense",                       # 运营费用
                "operating_income",                        # 营业利润
                "operating_margin",                        # 营业利润率
                "outstanding_shares",                      # 流通股数
                "research_and_development",                # 研发支出
                "return_on_invested_capital",              # 投资回报率
                "revenue",                                 # 收入
                "shareholders_equity",                     # 股东权益
                "total_assets",                           # 总资产
                "total_debt",                             # 总债务
                "total_liabilities",                      # 总负债
                "working_capital"                         # 营运资金
            ]
            search_line_items(ticker, common_items, end_date, force_refresh=force_refresh)
            
            # 加载内部交易
            get_insider_trades(ticker, end_date, start_date, force_refresh=force_refresh)
            
            # 加载公司新闻
            get_company_news(ticker, end_date, start_date, force_refresh=force_refresh)
            
            # 加载日级价格数据
            get_prices(ticker, start_date, end_date, interval="day", interval_multiplier=1, force_refresh=force_refresh)
            
            # 加载分钟级价格数据
            recent_start_date = (datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S') - timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
            get_prices(ticker, recent_start_date, end_date, interval="minute", interval_multiplier=1, force_refresh=force_refresh)
            
            logger.info(f"预加载 {ticker} 的基础数据完成")
        except Exception as e:
            logger.error(f"预加载 {ticker} 数据失败: {str(e)}")
            logger.error(traceback.format_exc())

     # 更新最后刷新时间
    from data.cache import get_cache
    cache = get_cache()
    cache.set_last_refresh_time(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 记录完成时间和总耗时
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    logger.info(f"所有股票数据预加载完成，总耗时: {duration:.2f}秒")
