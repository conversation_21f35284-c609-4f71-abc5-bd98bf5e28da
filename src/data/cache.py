from datetime import datetime
import threading
from data.database import SessionLocal, CacheData
from utils.json_utils import sanitize_json_data

class Cache:
    """API响应的内存缓存，支持数据库持久化。"""

    def __init__(self):
        self._prices_cache: dict[str, list[dict[str, any]]] = {}
        self._financial_metrics_cache: dict[str, list[dict[str, any]]] = {}
        self._line_items_cache: dict[str, list[dict[str, any]]] = {}
        self._insider_trades_cache: dict[str, list[dict[str, any]]] = {}
        self._company_news_cache: dict[str, list[dict[str, any]]] = {}
        self._last_refresh_time: str | None = None
        
        # 初始化时从数据库加载缓存
        self._load_from_db()
        
        # 添加缓存锁
        self._cache_locks: dict[str, threading.Lock] = {}

    def set_last_refresh_time(self, timestamp: str):
        """设置最后刷新时间"""
        db = SessionLocal()
        try:
            record = db.query(CacheData).filter_by(cache_type='last_refresh_time', ticker='global').first()
            now = datetime.now()
            
            if record:
                record.data = {'timestamp': timestamp}
                record.last_updated = now
            else:
                record = CacheData(
                    cache_type='last_refresh_time',
                    ticker='global',
                    data={'timestamp': timestamp},
                    last_updated=now
                )
                db.add(record)
            
            db.commit()
            self._last_refresh_time = timestamp
        except Exception as e:
            print(f"保存最后刷新时间失败: {e}")
            db.rollback()
        finally:
            db.close()
    
    def get_last_refresh_time(self) -> str | None:
        """获取最后刷新时间"""
        if self._last_refresh_time is None:
            db = SessionLocal()
            try:
                record = db.query(CacheData).filter_by(cache_type='last_refresh_time', ticker='global').first()
                if record and isinstance(record.data, dict):
                    self._last_refresh_time = record.data.get('timestamp')
            finally:
                db.close()
        return self._last_refresh_time

    def get_cache_lock(self, cache_type: str, key: str, **kwargs) -> threading.Lock:
        """获取缓存锁"""
        lock_key = self._get_cache_key(cache_type, key, **kwargs)
        if lock_key not in self._cache_locks:
            self._cache_locks[lock_key] = threading.Lock()
        return self._cache_locks[lock_key]
    
    def _get_cache_key(self, data_type: str, ticker: str, **kwargs) -> str:
        """生成缓存键"""
        key = f"{data_type}_{ticker}"
        if kwargs:
            # 将额外参数添加到键中
            params = "_".join(f"{k}_{v}" for k, v in sorted(kwargs.items()))
            key = f"{key}_{params}"
        return key
    
    def _merge_data(self, existing: list[dict] | None, new_data: list[dict], key_field: str) -> list[dict]:
        """合并现有和新数据，基于关键字段避免重复。"""
        if not existing:
            return new_data
    
        # 创建现有键的集合，用于O(1)查找
        existing_keys = {item.get(key_field, f"missing_{i}") for i, item in enumerate(existing)}
    
        # 只添加尚不存在的项目
        merged = existing.copy()
        merged.extend([item for item in new_data if item.get(key_field, f"new_{len(merged)}") not in existing_keys])
        return merged
    
    def _load_from_db(self):
        """从数据库加载缓存数据"""
        try:
            db = SessionLocal()
            cache_records = db.query(CacheData).all()
            
            from utils.logger import get_logger
            logger = get_logger(__name__)
            logger.info(f"开始从数据库加载缓存数据，共找到 {len(cache_records)} 条记录")
            
            # 记录每种类型加载的数量
            loaded_counts = {
                "prices": 0,
                "financial_metrics": 0,
                "line_items": 0,
                "insider_trades": 0,
                "company_news": 0
            }
            
            for record in cache_records:
                try:
                    if record.cache_type.startswith('prices_'):
                        parts = record.cache_type.split('_')
                        if len(parts) >= 3:
                            interval = parts[1]
                            interval_multiplier = int(parts[2])
                            if isinstance(record.data, list):
                                self.set_prices(record.ticker, record.data, interval=interval, 
                                             interval_multiplier=interval_multiplier, save_to_db=False)
                                loaded_counts["prices"] += 1
                                
                    elif record.cache_type == 'financial_metrics':
                        if isinstance(record.data, list):
                            self.set_financial_metrics(record.ticker, record.data, save_to_db=False)
                            loaded_counts["financial_metrics"] += 1
                            
                    elif record.cache_type == 'line_items':
                        if isinstance(record.data, list):
                            self.set_line_items(record.ticker, record.data, save_to_db=False)
                            loaded_counts["line_items"] += 1
                            
                    elif record.cache_type == 'insider_trades':
                        if isinstance(record.data, list):
                            self.set_insider_trades(record.ticker, record.data, save_to_db=False)
                            loaded_counts["insider_trades"] += 1
                            
                    elif record.cache_type == 'company_news':
                        if isinstance(record.data, list):
                            self.set_company_news(record.ticker, record.data, save_to_db=False)
                            loaded_counts["company_news"] += 1
                            
                    else:
                        logger.warning(f"未知的缓存类型: {record.cache_type}")
                        
                except Exception as e:
                    logger.error(f"加载缓存记录失败: {record.cache_type}, {record.ticker}, 错误: {str(e)}")
                    continue
                    
            # 记录加载结果
            logger.info(f"缓存加载完成: 价格数据 {loaded_counts['prices']} 条, "
                       f"财务指标 {loaded_counts['financial_metrics']} 条, "
                       f"财务项目 {loaded_counts['line_items']} 条, "
                       f"内部交易 {loaded_counts['insider_trades']} 条, "
                       f"公司新闻 {loaded_counts['company_news']} 条")
                       
            # 输出各类型数据项数量
            for cache_type in ["prices", "financial_metrics", "line_items", "insider_trades", "company_news"]:
                total_items = sum(len(data) for data in getattr(self, f"_{cache_type}_cache").values())
                logger.info(f"{cache_type} 总数据项: {total_items}")
                
        except Exception as e:
            import traceback
            logger.error(f"从数据库加载缓存失败: {e}")
            logger.error(traceback.format_exc())
        finally:
            if 'db' in locals():
                db.close()
    
    def _save_to_db(self, cache_type: str, ticker: str, data: list[dict]):
        """将缓存数据保存到数据库"""
        db = SessionLocal()
        try:
            # 查找现有记录
            record = db.query(CacheData).filter_by(cache_type=cache_type, ticker=ticker).first()
            
            now = datetime.now()
            
            # 为每个数据项添加最后更新时间
            for item in data:
                if "_last_updated" not in item:
                    # Ensure item is a dict before trying to assign to it
                    if isinstance(item, dict):
                        item["_last_updated"] = now.isoformat()
            
            sanitized_data = sanitize_json_data(data)
            if record:
                # 更新现有记录
                record.data = sanitized_data
                record.last_updated = now
            else:
                # 创建新记录
                record = CacheData(
                    cache_type=cache_type,
                    ticker=ticker,
                    data=sanitized_data
                )
                db.add(record)
                
            db.commit()
        except Exception as e:
            print(f"保存缓存到数据库失败: {e}")
            db.rollback()
        finally:
            db.close()

    def get_prices(self, ticker: str, interval: str = "day", interval_multiplier: int = 1) -> list[dict[str, any]] | None:
        """
        获取价格缓存数据。
        
        Args:
            ticker: 股票代码
            interval: 时间间隔
            interval_multiplier: 间隔乘数
            
        Returns:
            缓存的价格数据列表，如果不存在则返回 None
        """
        cache_key = f"{ticker}_{interval}_{interval_multiplier}"
        return self._prices_cache.get(cache_key)

    def set_prices(self, ticker: str, data: list[dict[str, any]], interval: str = "day", interval_multiplier: int = 1, save_to_db: bool = True):
        """将新的价格数据添加到缓存中并可选保存到数据库。"""
        cache_key = f"{ticker}_{interval}_{interval_multiplier}"
        cache_type = f"prices_{interval}_{interval_multiplier}"
        
        merged_data = self._merge_data(self._prices_cache.get(cache_key), data, key_field="time")
        self._prices_cache[cache_key] = merged_data

        if save_to_db:
            self._save_to_db(cache_type, ticker, merged_data)

    def get_financial_metrics(self, ticker: str) -> list[dict[str, any]] | None:
        """获取缓存的财务指标"""
        return self._financial_metrics_cache.get(ticker)

    def set_financial_metrics(self, ticker: str, data: list[dict[str, any]], save_to_db: bool = True):
        """将新的财务指标添加到缓存中并可选保存到数据库。"""
        merged_data = self._merge_data(self._financial_metrics_cache.get(ticker), data, key_field="report_period")
        self._financial_metrics_cache[ticker] = merged_data
        if save_to_db:
            self._save_to_db('financial_metrics', ticker, merged_data)
        
    def get_line_items(self, ticker: str) -> list[dict[str, any]] | None:
        """获取缓存的财务项目"""
        return self._line_items_cache.get(ticker)

    def set_line_items(self, ticker: str, data: list[dict[str, any]], save_to_db: bool = True):
        """将新的财务项目添加到缓存中并可选保存到数据库。"""
        merged_data = self._merge_data(self._line_items_cache.get(ticker), data, key_field="_unique_key")
        self._line_items_cache[ticker] = merged_data
        if save_to_db:
            self._save_to_db('line_items', ticker, merged_data)
        
    def get_insider_trades(self, ticker: str) -> list[dict[str, any]] | None:
        """获取缓存的内部交易数据"""
        return self._insider_trades_cache.get(ticker)

    def set_insider_trades(self, ticker: str, data: list[dict[str, any]], save_to_db: bool = True):
        """将新的内部交易数据添加到缓存中并可选保存到数据库。"""
        merged_data = self._merge_data(self._insider_trades_cache.get(ticker), data, key_field="_unique_key")
        self._insider_trades_cache[ticker] = merged_data
        if save_to_db:
            self._save_to_db('insider_trades', ticker, merged_data)
        
    def get_company_news(self, ticker: str) -> list[dict[str, any]] | None:
        """获取缓存的公司新闻"""
        return self._company_news_cache.get(ticker)

    def set_company_news(self, ticker: str, data: list[dict[str, any]], save_to_db: bool = True):
        """将新的公司新闻添加到缓存中并可选保存到数据库。"""
        for item in data:
            if "_unique_key" not in item:
                item["_unique_key"] = item.get("id", f"{item.get('date', '')}_{item.get('title', '')}"[:100])
        
        merged_data = self._merge_data(self._company_news_cache.get(ticker), data, key_field="_unique_key")
        self._company_news_cache[ticker] = merged_data
        if save_to_db:
            self._save_to_db('company_news', ticker, merged_data)
        

# 全局缓存实例
_cache = Cache()


def get_cache() -> Cache:
    """获取全局缓存实例。"""
    return _cache
