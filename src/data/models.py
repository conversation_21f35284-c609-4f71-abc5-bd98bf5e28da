from pydantic import BaseModel


class Price(BaseModel):
    """价格数据模型"""
    open: float  # 开盘价
    close: float  # 收盘价
    high: float  # 最高价
    low: float  # 最低价
    volume: int  # 成交量
    time: str  # 时间


class PriceResponse(BaseModel):
    """价格响应模型"""
    ticker: str  # 股票代码
    prices: list[Price]  # 价格列表


class FinancialMetrics(BaseModel):
    """财务指标模型"""
    ticker: str  # 股票代码
    report_period: str  # 报告期间
    period: str  # 期间类型
    currency: str  # 货币
    market_cap: float | None  # 市值
    enterprise_value: float | None  # 企业价值
    price_to_earnings_ratio: float | None  # 市盈率
    price_to_book_ratio: float | None  # 市净率
    price_to_sales_ratio: float | None  # 市销率
    enterprise_value_to_ebitda_ratio: float | None  # 企业价值/EBITDA比率
    enterprise_value_to_revenue_ratio: float | None  # 企业价值/收入比率
    free_cash_flow_yield: float | None  # 自由现金流收益率
    peg_ratio: float | None  # PEG比率
    gross_margin: float | None  # 毛利率
    operating_margin: float | None  # 营业利润率
    net_margin: float | None  # 净利润率
    return_on_equity: float | None  # 股本回报率
    return_on_assets: float | None  # 资产回报率
    return_on_invested_capital: float | None  # 投资资本回报率
    asset_turnover: float | None  # 资产周转率
    inventory_turnover: float | None  # 库存周转率
    receivables_turnover: float | None  # 应收账款周转率
    days_sales_outstanding: float | None  # 应收账款周转天数
    operating_cycle: float | None  # 营业周期
    working_capital_turnover: float | None  # 营运资金周转率
    current_ratio: float | None  # 流动比率
    quick_ratio: float | None  # 速动比率
    cash_ratio: float | None  # 现金比率
    operating_cash_flow_ratio: float | None  # 经营现金流比率
    debt_to_equity: float | None  # 债务权益比
    debt_to_assets: float | None  # 债务资产比
    interest_coverage: float | None  # 利息覆盖率
    revenue_growth: float | None  # 收入增长率
    earnings_growth: float | None  # 盈利增长率
    book_value_growth: float | None  # 账面价值增长率
    earnings_per_share_growth: float | None  # 每股收益增长率
    free_cash_flow_growth: float | None  # 自由现金流增长率
    operating_income_growth: float | None  # 营业收入增长率
    ebitda_growth: float | None  # EBITDA增长率
    payout_ratio: float | None  # 派息比率
    earnings_per_share: float | None  # 每股收益
    book_value_per_share: float | None  # 每股账面价值
    free_cash_flow_per_share: float | None  # 每股自由现金流


class FinancialMetricsResponse(BaseModel):
    """财务指标响应模型"""
    financial_metrics: list[FinancialMetrics]  # 财务指标列表


class LineItem(BaseModel):
    """财务项目模型"""
    ticker: str  # 股票代码
    report_period: str  # 报告期间
    period: str  # 期间类型
    currency: str  # 货币

    # 允许动态添加额外字段
    model_config = {"extra": "allow"}


class LineItemResponse(BaseModel):
    """财务项目响应模型"""
    search_results: list[LineItem]  # 搜索结果列表


class InsiderTrade(BaseModel):
    """内部交易模型"""
    ticker: str  # 股票代码
    issuer: str | None  # 发行人
    name: str | None  # 交易者姓名
    title: str | None  # 职位
    is_board_director: bool | None  # 是否为董事会成员
    transaction_date: str | None  # 交易日期
    transaction_shares: float | None  # 交易股数
    transaction_price_per_share: float | None  # 每股交易价格
    transaction_value: float | None  # 交易价值
    shares_owned_before_transaction: float | None  # 交易前持有股数
    shares_owned_after_transaction: float | None  # 交易后持有股数
    security_title: str | None  # 证券名称
    filing_date: str  # 申报日期


class InsiderTradeResponse(BaseModel):
    """内部交易响应模型"""
    insider_trades: list[InsiderTrade]  # 内部交易列表


class CompanyNews(BaseModel):
    """公司新闻模型"""
    ticker: str  # 股票代码
    title: str  # 标题
    author: str  # 作者
    source: str  # 来源
    date: str  # 日期
    url: str  # 链接
    sentiment: str | None = None  # 情感分析


class CompanyNewsResponse(BaseModel):
    """公司新闻响应模型"""
    news: list[CompanyNews]  # 新闻列表


class CompanyFacts(BaseModel):
    ticker: str
    name: str
    cik: str | None = None
    industry: str | None = None
    sector: str | None = None
    category: str | None = None
    exchange: str | None = None
    is_active: bool | None = None
    listing_date: str | None = None
    location: str | None = None
    market_cap: float | None = None
    number_of_employees: int | None = None
    sec_filings_url: str | None = None
    sic_code: str | None = None
    sic_industry: str | None = None
    sic_sector: str | None = None
    website_url: str | None = None
    weighted_average_shares: int | None = None


class CompanyFactsResponse(BaseModel):
    company_facts: CompanyFacts


class Position(BaseModel):
    """持仓位置模型"""
    cash: float = 0.0  # 现金
    shares: int = 0  # 持有股数
    ticker: str  # 股票代码


class Portfolio(BaseModel):
    """投资组合模型"""
    positions: dict[str, Position]  # 持仓映射（股票代码 -> 持仓）
    total_cash: float = 0.0  # 总现金


class AnalystSignal(BaseModel):
    """分析师信号模型"""
    signal: str | None = None  # 信号
    confidence: float | None = None  # 置信度
    reasoning: dict | str | None = None  # 推理依据
    max_position_size: float | None = None  # 风险管理信号的最大持仓规模


class TickerAnalysis(BaseModel):
    """股票分析模型"""
    ticker: str  # 股票代码
    analyst_signals: dict[str, AnalystSignal]  # 分析师信号映射（代理名称 -> 信号）


class AgentStateData(BaseModel):
    """代理状态数据模型"""
    tickers: list[str]  # 股票代码列表
    portfolio: Portfolio  # 投资组合
    start_date: str  # 开始日期
    end_date: str  # 结束日期
    ticker_analyses: dict[str, TickerAnalysis]  # 股票分析映射（股票代码 -> 分析）


class AgentStateMetadata(BaseModel):
    """代理状态元数据模型"""
    show_reasoning: bool = False  # 是否显示推理过程
    model_config = {"extra": "allow"}  # 允许额外字段
