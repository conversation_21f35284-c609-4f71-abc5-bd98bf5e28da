from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, JSON, Text, Boolean, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
import uuid
from flask_login import UserMixin

# 数据库连接配置
DATABASE_URL = os.getenv('DATABASE_URL', '********************************************/aifund')

# 增加连接池配置
engine = create_engine(
    DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,  # 每次从连接池获取连接时检查连接是否有效
    pool_recycle=3600    # 1小时后回收连接
)
SessionLocal = sessionmaker(bind=engine)

Base = declarative_base()

# 创建表
def init_db():
    Base.metadata.create_all(engine)

class TradingRecord(Base):
    __tablename__ = 'trading_records'
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=lambda: datetime.now())
    type = Column(String)  # 'trading' 或 'backtest'
    tickers = Column(JSON)
    selected_analysts = Column(JSON)
    model_choice = Column(String)
    initial_cash = Column(Float)
    margin_requirement = Column(Float)
    trading_date = Column(String)
    start_date = Column(String, nullable=True)
    end_date = Column(String, nullable=True)
    interval = Column(String, nullable=True)
    interval_multiplier = Column(Integer, nullable=True, default=1)  # 添加间隔乘数字段
    show_reasoning = Column(Boolean)
    decisions = Column(JSON)
    analyst_signals = Column(JSON)
    execution_log = Column(Text)
    metrics = Column(JSON, nullable=True)  # 用于回测结果
    portfolio_values = Column(JSON, nullable=True)  # 用于回测结果
    trade_history = Column(JSON, nullable=True)  # 历史记录字段，用于存储交易决策表格数据
    daily_summaries = Column(JSON, nullable=True)  # 存储每日摘要信息，允许为空
    daily_decisions = Column(JSON, nullable=True)  # 存储每日决策信息，包括理由
    daily_analyst_signals = Column(JSON, nullable=True)  # 存储每日分析师信号


# 缓存表定义
class CacheData(Base):
    __tablename__ = 'cache_data'
    
    id = Column(Integer, primary_key=True)
    cache_type = Column(String)  # 'prices_day_1', 'prices_minute_5', 'financial_metrics', 'line_items', 'insider_trades', 'company_news'
    ticker = Column(String)
    data = Column(JSON)
    last_updated = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now())
    
    # 联合唯一约束，确保每个类型和ticker只有一条记录
    __table_args__ = (
        UniqueConstraint('cache_type', 'ticker', name='uix_cache_type_ticker'),
    )

# 定义任务模型
class ScheduledTask(Base):
    __tablename__ = 'scheduled_tasks'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=True)
    tickers = Column(String(255), nullable=False)
    execution_interval = Column(String(10), nullable=False)  # 1m, 5m, 15m, 30m, 1h, 4h, 1d
    created_at = Column(DateTime, default=lambda: datetime.now())
    next_run = Column(DateTime, nullable=False)
    last_run = Column(DateTime, nullable=True)
    status = Column(String(20), default='active')  # active, paused, completed, error
    execution_count = Column(Integer, default=0)
    end_condition = Column(String(20), default='never')  # never, count, date
    end_count = Column(Integer, nullable=True)
    end_date = Column(DateTime, nullable=True)
    
    # 任务配置
    selected_analysts = Column(JSON, nullable=False)
    model_choice = Column(String(50), nullable=False)
    initial_cash = Column(Float, default=100000.0)
    margin_requirement = Column(Float, default=0.0)
    interval = Column(String(10), default='5m')
    interval_multiplier = Column(Integer, default=1) 
    show_reasoning = Column(Boolean, default=True)
    
    # 任务元数据
    task_data = Column(JSON, nullable=True)  # 存储任务的其他数据
    
    # 执行时段字段
    execution_time_start = Column(String(5), nullable=True)  # 格式: "21:30"
    execution_time_end = Column(String(5), nullable=True)    # 格式: "04:30"

# 用户会话模型
# 查找SimpleUser类的定义，并确保它正确存储和返回用户名
class SimpleUser(UserMixin):
    """简单的用户类，用于开发模式"""
    def __init__(self, user_id, username):
        self.id = user_id
        self._username = username
    
    @property
    def username(self):
        return self._username
    
    def get_id(self):
        return str(self.id)

class User(Base, UserMixin):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now())
    last_login = Column(DateTime, nullable=True)
    
    def get_id(self):
        """返回用户ID，用于Flask-Login"""
        return str(self.id)

# 定义任务执行历史模型
class TaskExecution(Base):
    __tablename__ = 'task_executions'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    task_id = Column(String(36), nullable=False)
    task_name = Column(String(255), nullable=True)
    execution_time = Column(DateTime, default=lambda: datetime.now())
    status = Column(String(20), default='success')  # success, failed
    record_id = Column(Integer, nullable=True)  # 关联到TradingRecord的ID
    error_message = Column(Text, nullable=True)
