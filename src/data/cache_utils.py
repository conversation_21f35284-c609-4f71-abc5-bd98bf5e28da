from datetime import datetime, timedelta
from data.cache import get_cache
from utils.logger import get_logger

# 添加模块级别的日志记录器
logger = get_logger(__name__)

def normalize_timestamp_string(timestamp_str: str) -> datetime:
    """
    将时间戳字符串标准化为datetime对象
    
    Args:
        timestamp_str: 时间戳字符串
        
    Returns:
        datetime: 标准化后的datetime对象
    """
    # 处理可能的'Z'结尾（UTC时间）
    if timestamp_str.endswith('Z'):
        timestamp_str = timestamp_str.replace('Z', '+00:00')
    
    # 尝试解析ISO格式
    try:
        return datetime.fromisoformat(timestamp_str)
    except ValueError:
        # 如果解析失败，尝试其他常见格式
        try:
            return datetime.strptime(timestamp_str, "%Y-%m-%d")
        except ValueError:
            # 如果还是失败，返回当前时间（这将触发缓存刷新）
            logger.warning(f"无法解析时间戳: {timestamp_str}，使用默认值")
            return datetime.now() - timedelta(days=365)  # 返回一年前的时间，确保刷新


def standardize_time_format(time_str: str) -> str:
    """
    标准化时间格式，确保时间字符串格式一致
    
    Args:
        time_str: 输入的时间字符串
        
    Returns:
        标准化后的时间字符串
    """
    try:
        # 尝试解析时间字符串
        dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        # 返回标准格式
        return dt.strftime("%Y-%m-%d %H:%M")
    except ValueError:
        # 如果解析失败，返回原始字符串
        return time_str