from typing_extensions import Annotated, Sequence, TypedDict

import operator
from langchain_core.messages import BaseMessage


import json


def merge_dicts(a: dict[str, any], b: dict[str, any]) -> dict[str, any]:
    """合并两个字典"""
    return {**a, **b}


# 定义代理状态
class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], operator.add]
    data: Annotated[dict[str, any], merge_dicts]
    metadata: Annotated[dict[str, any], merge_dicts]


def show_agent_reasoning(output, agent_name):
    """显示代理推理过程"""
    print(f"\n{'=' * 10} {agent_name.center(28)} {'=' * 10}")

    def convert_to_serializable(obj):
        """将对象转换为可序列化格式"""
        if hasattr(obj, "to_dict"):  # 处理Pandas Series/DataFrame
            return obj.to_dict()
        elif hasattr(obj, "__dict__"):  # 处理自定义对象
            return obj.__dict__
        elif isinstance(obj, (int, float, bool, str)):
            return obj
        elif isinstance(obj, (list, tuple)):
            return [convert_to_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: convert_to_serializable(value) for key, value in obj.items()}
        else:
            return str(obj)  # 回退到字符串表示

    if isinstance(output, (dict, list)):
        # 将输出转换为JSON可序列化格式
        serializable_output = convert_to_serializable(output)
        print(json.dumps(serializable_output, indent=2, ensure_ascii=False))
    else:
        try:
            # 将字符串解析为JSON并美化打印
            parsed_output = json.loads(output)
            print(json.dumps(parsed_output, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            # 如果不是有效的JSON，回退到原始字符串
            print(output)

    print("=" * 48)
