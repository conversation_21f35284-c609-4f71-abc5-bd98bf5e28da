import os
from langchain_openai import ChatOpenAI
from enum import Enum
from pydantic import BaseModel
from typing import Tuple


class ModelProvider(str, Enum):
    """支持的LLM提供商枚举"""
    OPENAI = "OpenAI"


class LLMModel(BaseModel):
    """表示LLM模型配置"""
    display_name: str
    model_name: str
    provider: ModelProvider

    def to_choice_tuple(self) -> Tuple[str, str, str]:
        """转换为questionary选项所需的格式"""
        return (self.display_name, self.model_name, self.provider.value)
    
    def has_json_mode(self) -> bool:
        """检查模型是否支持JSON模式"""
        return not self.is_deepseek() and not self.is_gemini()
    
    def is_deepseek(self) -> bool:
        """检查模型是否为DeepSeek模型"""
        return self.model_name.startswith("deepseek")
    
    def is_gemini(self) -> bool:
        """检查模型是否为Gemini模型"""
        return self.model_name.startswith("gemini")


# 定义可用模型
AVAILABLE_MODELS = [
    LLMModel(
        display_name="[deepseek] deepseek-r1",
        model_name="deepseek-r1",
        provider=ModelProvider.OPENAI
    ),
    LLMModel(
        display_name="[deepseek] deepseek-v3",
        model_name="deepseek-v3",
        provider=ModelProvider.OPENAI
    ),
    LLMModel(
        display_name="[gemini] gemini-2.0-flash",
        model_name="gemini-2.0-flash",
        provider=ModelProvider.OPENAI
    ),
    LLMModel(
        display_name="[openai] gpt-4o",
        model_name="gpt-4o",
        provider=ModelProvider.OPENAI
    ),
    # LLMModel(
    #     display_name="[openai] o1",
    #     model_name="o1",
    #     provider=ModelProvider.OPENAI
    # ),
    LLMModel(
        display_name="[qwen] qwen3-235b-a22b",
        model_name="qwen3-235b-a22b",
        provider=ModelProvider.OPENAI
    ),
    LLMModel(
        display_name="[qwen] qwen3-32b",
        model_name="qwen3-32b",
        provider=ModelProvider.OPENAI
    ),
]

# 设置默认模型
DEFAULT_MODEL = "deepseek-r1"

# 创建UI预期格式的LLM_ORDER
LLM_ORDER = [model.to_choice_tuple() for model in AVAILABLE_MODELS]

def get_model_info(model_name: str) -> LLMModel | None:
    """通过model_name获取模型信息"""
    return next((model for model in AVAILABLE_MODELS if model.model_name == model_name), None)

def get_model(model_name: str, model_provider: ModelProvider) -> ChatOpenAI | None:
    if model_provider == ModelProvider.OPENAI:
        # 获取并验证API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        if not api_key:
            # 向控制台打印错误
            print(f"API密钥错误：请确保在.env文件中设置了OPENAI_API_KEY。")
            raise ValueError("未找到OpenAI API密钥。请确保在.env文件中设置了OPENAI_API_KEY。")
        return ChatOpenAI(model=model_name, api_key=api_key, base_url=base_url)
    else:
        raise ValueError(f"不支持的模型提供商: {model_provider}")   