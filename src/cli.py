#!/usr/bin/env python
import sys
import argparse
from utils.display import print_trading_output
from utils.analysts import ANALYST_ORDER
from llm.models import LLM_ORDER, get_model_info
from trading.trader import run_hedge_fund, create_workflow
from utils.time_utils import STANDARD_TIMEZONE
from datetime import datetime
from dateutil.relativedelta import relativedelta
from utils.visualize import save_graph_as_png
from colorama import Fore, Back, Style, init
import questionary

init(autoreset=True)

def main():
    parser = argparse.ArgumentParser(description="运行对冲基金交易系统")
    parser.add_argument(
        "--initial-cash",
        type=float,
        default=100000.0,
        help="初始现金头寸。默认为100000.0"
    )
    parser.add_argument(
        "--margin-requirement",
        type=float,
        default=0.0,
        help="初始保证金要求。默认为0.0"
    )
    parser.add_argument("--tickers", type=str, help="逗号分隔的股票代码符号列表")
    parser.add_argument(
        "--start-date",
        type=str,
        help="开始日期（YYYY-MM-DD）。默认为结束日期前3个月",
    )
    parser.add_argument("--end-date", type=str, help="结束日期（YYYY-MM-DD）。默认为今天")
    parser.add_argument("--show-reasoning", action="store_true", help="显示每个代理的推理过程")
    parser.add_argument(
        "--show-agent-graph", action="store_true", help="显示代理图"
    )

    args = parser.parse_args()
    
    # 检查是否提供了股票代码
    if not args.tickers:
        print(f"{Fore.RED}错误: 必须提供股票代码。使用 --tickers 参数。{Style.RESET_ALL}")
        parser.print_help()
        sys.exit(1)

    # 从逗号分隔的字符串解析股票代码
    tickers = [ticker.strip() for ticker in args.tickers.split(",")]

    # 选择分析师
    selected_analysts = None
    choices = questionary.checkbox(
        "选择您的AI分析师。",
        choices=[questionary.Choice(display, value=value) for display, value in ANALYST_ORDER],
        instruction="\n\n说明: \n1. 按空格键选择/取消选择分析师。\n2. 按'a'选择/取消选择全部。\n3. 完成后按回车键运行对冲基金。\n",
        validate=lambda x: len(x) > 0 or "您必须至少选择一个分析师。",
        style=questionary.Style(
            [
                ("checkbox-selected", "fg:green"),
                ("selected", "fg:green noinherit"),
                ("highlighted", "noinherit"),
                ("pointer", "noinherit"),
            ]
        ),
    ).ask()

    if not choices:
        print("\n\n收到中断信号。退出...")
        sys.exit(0)
    else:
        selected_analysts = choices
        print(f"\n已选择的分析师: {', '.join(Fore.GREEN + choice.title().replace('_', ' ') + Style.RESET_ALL for choice in choices)}\n")

    # 选择LLM模型
    model_choice = questionary.select(
        "选择您的LLM模型:",
        choices=[questionary.Choice(display, value=value) for display, value, _ in LLM_ORDER],
        style=questionary.Style([
            ("selected", "fg:green bold"),
            ("pointer", "fg:green bold"),
            ("highlighted", "fg:green"),
            ("answer", "fg:green bold"),
        ])
    ).ask()

    if not model_choice:
        print("\n\n收到中断信号。退出...")
        sys.exit(0)
    else:
        # 使用辅助函数获取模型信息
        model_info = get_model_info(model_choice)
        if model_info:
            model_provider = model_info.provider.value
            print(f"\n已选择 {Fore.CYAN}{model_provider}{Style.RESET_ALL} 模型: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")
        else:
            model_provider = "未知"
            print(f"\n已选择模型: {Fore.GREEN + Style.BRIGHT}{model_choice}{Style.RESET_ALL}\n")

    # 使用选定的分析师创建工作流
    workflow = create_workflow(selected_analysts)
    app = workflow.compile()

    if args.show_agent_graph:
        file_path = ""
        if selected_analysts is not None:
            for selected_analyst in selected_analysts:
                file_path += selected_analyst + "_"
            file_path += "graph.png"
        save_graph_as_png(app, file_path)

    # 验证提供的日期
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("开始日期必须采用YYYY-MM-DD格式")

    if args.end_date:
        try:
            datetime.strptime(args.end_date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("结束日期必须采用YYYY-MM-DD格式")

    # 设置开始和结束日期
    end_date = args.end_date or datetime.now().strftime("%Y-%m-%d")
    if not args.start_date:
        # 计算结束日期前3个月
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        start_date = (end_date_obj - relativedelta(months=3)).strftime("%Y-%m-%d")
    else:
        start_date = args.start_date

    # 初始化投资组合，包括现金金额和股票头寸
    portfolio = {
        "cash": args.initial_cash,  # 初始现金金额
        "margin_requirement": args.margin_requirement,  # 初始保证金要求
        "margin_used": 0.0,  # 所有空头头寸的总保证金使用量
        "positions": {
            ticker: {
                "long": 0,  # 持有的多头股票数量
                "short": 0,  # 持有的空头股票数量
                "long_cost_basis": 0.0,  # 多头头寸的平均成本基础
                "short_cost_basis": 0.0,  # 卖空股票的平均价格
                "short_margin_used": 0.0,  # 用于此股票空头的保证金美元
            } for ticker in tickers
        },
        "realized_gains": {
            ticker: {
                "long": 0.0,  # 多头头寸的已实现收益
                "short": 0.0,  # 空头头寸的已实现收益
            } for ticker in tickers
        }
    }

    # 运行对冲基金
    result = run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        show_reasoning=args.show_reasoning,
        selected_analysts=selected_analysts,
        model_name=model_choice,
        model_provider=model_provider,
    )
    print_trading_output(result)