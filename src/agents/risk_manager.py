from langchain_core.messages import HumanMessage
from graph.state import AgentState, show_agent_reasoning
from utils.progress import progress
from tools.api import get_prices, prices_to_df
from datetime import datetime, timedelta
import json
import numpy as np


##### 风险管理代理 #####
VOLATILITY_LOOKBACK_DAYS = 252  # 用于波动率计算的交易日数量
VOLATILITY_CALENDAR_DAY_BUFFER = int(VOLATILITY_LOOKBACK_DAYS * 1.7) # 为获取足够的交易日数据，回溯更多日历日

def risk_management_agent(state: AgentState):
    """基于多个股票的实际风险因素控制仓位大小。"""
    portfolio = state["data"]["portfolio"]
    data = state["data"]
    tickers = data["tickers"]

    # 为每个股票初始化风险分析
    risk_analysis = {}
    current_prices = {}  # 在这里存储价格以避免重复API调用
    force_refresh = data.get("force_refresh", False)
    current_analysis_date_str = data["end_date"] # 这是分析的“当前日期”, 可能是 str 或 datetime

    # 基于 current_analysis_date_str 的类型正确初始化 current_analysis_date_dt
    if isinstance(current_analysis_date_str, str):
        # 如果是字符串，则解析日期部分（可能包含时间）
        current_analysis_date_dt = datetime.fromisoformat(current_analysis_date_str.split('T')[0])
    elif isinstance(current_analysis_date_str, datetime):
        # 如果已经是 datetime 对象，则将其规范化为当天的开始时间
        current_analysis_date_dt = current_analysis_date_str.replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        raise TypeError(f"end_date ('current_analysis_date_str') 必须是字符串或 datetime 对象，得到 {type(current_analysis_date_str)}")

    for ticker in tickers:
        progress.update_status("risk_management_agent", ticker, "获取价格数据用于风险分析")
        # 1. 获取日线数据用于计算波动率
        volatility_start_date_str = (current_analysis_date_dt - timedelta(days=VOLATILITY_CALENDAR_DAY_BUFFER)).strftime("%Y-%m-%d")
        daily_prices_list = get_prices(
            ticker=ticker,
            start_date=volatility_start_date_str,
            end_date=current_analysis_date_str,
            interval="day",
            force_refresh=force_refresh
        )
        
        volatility = 0.5 # 默认波动率（中性）
        if daily_prices_list and len(daily_prices_list) >= 21: # 需要至少一些数据点来计算波动率
            daily_prices_df = prices_to_df(daily_prices_list)
            # 确保我们有足够的数据点
            if not daily_prices_df.empty and len(daily_prices_df) >= 21:
                 # 年化波动率
                volatility = daily_prices_df["close"].pct_change().std() * (252 ** 0.5)
                if np.isnan(volatility) or np.isinf(volatility):
                    volatility = 0.5 # 如果计算出问题，则回退到默认值
            else:
                progress.update_status("risk_management_agent", ticker, "日线数据不足以计算波动率，使用默认值")
        else:
            progress.update_status("risk_management_agent", ticker, "未找到日线价格数据计算波动率，使用默认值")

        # 2. 获取最新价格数据（基于传入的interval，可能是分钟或日）
        # 为了获取最新的价格，我们只需要查询end_date当天的数据
        # 如果是分钟数据，API应该能返回当天的所有分钟点，我们取最后一个
        # 如果是日数据，API应该返回当天的日线点
        # 为了安全，可以稍微扩大查询范围，例如 end_date 的前一天到 end_date
        latest_price_start_date_str = (current_analysis_date_dt - timedelta(days=1)).strftime("%Y-%m-%d")
        latest_prices_list = get_prices(
            ticker=ticker,
            start_date=latest_price_start_date_str, # 查询从前一天到当前分析日期的数据
            end_date=current_analysis_date_str,
            interval=data.get("interval", "minute"),
            interval_multiplier=data.get("interval_multiplier", 1),
            force_refresh=force_refresh
        )

        if not latest_prices_list:
            progress.update_status("risk_management_agent", ticker, "失败：未找到最新价格数据")
            risk_analysis[ticker] = { # 提供默认值以避免后续错误
                "remaining_position_limit": 0.0,
                "current_price": 0.0,
                "confidence": 0,
                "reasoning": {"error": "未找到最新价格数据"},
            }
            continue

        latest_prices_df = prices_to_df(latest_prices_list)
        if latest_prices_df.empty:
            progress.update_status("risk_management_agent", ticker, "失败：最新价格数据为空")
            risk_analysis[ticker] = { # 提供默认值
                "remaining_position_limit": 0.0,
                "current_price": 0.0,
                "confidence": 0,
                "reasoning": {"error": "最新价格数据为空"},
            }
            continue
        
        current_price = latest_prices_df["close"].iloc[-1]
        current_prices[ticker] = current_price  # 存储当前价格

        progress.update_status("risk_management_agent", ticker, "计算仓位限制")
        # 计算此股票的当前仓位价值
        current_position_value = portfolio.get("cost_basis", {}).get(ticker, 0)

        # 使用存储的价格计算总投资组合价值
        total_portfolio_value = portfolio.get("cash", 0) + sum(portfolio.get("cost_basis", {}).get(t, 0) for t in portfolio.get("cost_basis", {}))

        # 任何单一仓位的基本限制是投资组合的20%
        position_limit = total_portfolio_value * 0.20

        # 对于现有仓位，从限制中减去当前仓位价值
        remaining_position_limit = position_limit - current_position_value

        # 确保不超过可用现金
        max_position_size = min(remaining_position_limit, portfolio.get("cash", 0))

        # 计算信心度（基于波动率和剩余仓位限制）
        confidence = min(100, round((1 - volatility) * 100)) if volatility < 1 else 0
        risk_analysis[ticker] = {
            "remaining_position_limit": float(max_position_size),
            "current_price": float(current_price),
            "confidence": confidence,
            "reasoning": {
                "portfolio_value": float(total_portfolio_value),
                "current_position": float(current_position_value),
                "position_limit": float(position_limit),
                "remaining_limit": float(remaining_position_limit),
                "available_cash": float(portfolio.get("cash", 0)),
            },
        }

        progress.update_status("risk_management_agent", ticker, "完成")

    message = HumanMessage(
        content=json.dumps(risk_analysis),
        name="risk_management_agent",
    )

    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(risk_analysis, "风险管理代理")

    # 将信号添加到分析师信号列表
    state["data"]["analyst_signals"]["risk_management_agent"] = risk_analysis

    return {
        "messages": state["messages"] + [message],
        "data": data,
    }
