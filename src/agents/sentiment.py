from langchain_core.messages import HumanMessage
from graph.state import AgentState, show_agent_reasoning
from utils.progress import progress
import pandas as pd
import numpy as np
import json

from tools.api import get_insider_trades, get_company_news


##### 情绪分析代理 #####
def sentiment_analyst_agent(state: AgentState):
    """分析市场情绪并为多个股票生成交易信号。"""
    data = state.get("data", {})
    end_date = data.get("end_date")
    tickers = data.get("tickers")

    # 为每个股票初始化情绪分析
    sentiment_analysis = {}

    for ticker in tickers:
        progress.update_status("sentiment_analyst_agent", ticker, "获取内部交易数据")

        # 获取内部交易数据
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=1000,
        )

        progress.update_status("sentiment_analyst_agent", ticker, "分析交易模式")

        # 从内部交易获取信号
        transaction_shares = pd.Series([t.transaction_shares for t in insider_trades]).dropna()
        insider_signals = np.where(transaction_shares < 0, "bearish", "bullish").tolist()

        progress.update_status("sentiment_analyst_agent", ticker, "获取公司新闻")

        # 获取公司新闻
        company_news = get_company_news(ticker, end_date, limit=100)

        # 从公司新闻获取情绪
        sentiment = pd.Series([n.sentiment for n in company_news]).dropna()
        news_signals = np.where(sentiment == "negative", "bearish", 
                              np.where(sentiment == "positive", "bullish", "neutral")).tolist()
        
        progress.update_status("sentiment_analyst_agent", ticker, "合并信号")
        # 使用权重合并两个来源的信号
        insider_weight = 0.3
        news_weight = 0.7
        
        # 计算加权信号计数
        bullish_signals = (
            insider_signals.count("bullish") * insider_weight +
            news_signals.count("bullish") * news_weight
        )
        bearish_signals = (
            insider_signals.count("bearish") * insider_weight +
            news_signals.count("bearish") * news_weight
        )

        if bullish_signals > bearish_signals:
            overall_signal = "bullish"
        elif bearish_signals > bullish_signals:
            overall_signal = "bearish"
        else:
            overall_signal = "neutral"

        # 基于加权比例计算置信度
        total_weighted_signals = len(insider_signals) * insider_weight + len(news_signals) * news_weight
        confidence = 0  # 没有信号时的默认置信度
        if total_weighted_signals > 0:
            confidence = round((max(bullish_signals, bearish_signals) / total_weighted_signals) * 100, 2)
        reasoning = f"加权看涨信号: {bullish_signals:.1f}, 加权看跌信号: {bearish_signals:.1f}"

        sentiment_analysis[ticker] = {
            "signal": overall_signal,
            "confidence": confidence,
            "reasoning": reasoning,
        }

        progress.update_status("sentiment_analyst_agent", ticker, "Done", analysis=json.dumps(reasoning, indent=4))

    # 创建情绪分析消息
    message = HumanMessage(
        content=json.dumps(sentiment_analysis),
        name="sentiment_analyst_agent",
    )

    # 如果设置了标志，打印推理过程
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(sentiment_analysis, "情绪分析代理")

    # 将信号添加到分析师信号列表
    state["data"]["analyst_signals"]["sentiment_analyst_agent"] = sentiment_analysis

    progress.update_status("sentiment_analyst_agent", None, "Done")

    return {
        "messages": [message],
        "data": data,
    }
