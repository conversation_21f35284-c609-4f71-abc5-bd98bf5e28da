import os
import pandas as pd
import requests
from datetime import datetime, timedelta

from data.cache import get_cache
from data.models import (
    CompanyNews,
    CompanyNewsResponse,
    FinancialMetrics,
    FinancialMetricsResponse,
    Price,
    PriceResponse,
    LineItem,
    LineItemResponse,
    InsiderTrade,
    InsiderTradeResponse,
    CompanyFactsResponse,
)
from utils.logger import get_logger
from utils.time_utils import normalize_timestamp, normalize_date_string, get_utc_date_for_api_start, get_utc_date_for_api_end
from config import NO_KEY_REQUIRED_TICKERS

# 获取日志记录器
logger = get_logger(__name__)

# 全局缓存实例
_cache = get_cache()

# 检查是否需要API密钥的辅助函数
def _needs_api_key(ticker: str) -> bool:
    """检查给定的股票代码是否需要API密钥"""
    return ticker not in NO_KEY_REQUIRED_TICKERS

# 获取适当的请求头
def _get_headers(ticker: str) -> dict:
    """根据股票代码获取适当的请求头"""
    headers = {}
    if _needs_api_key(ticker) and (api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY")):
        headers["X-API-KEY"] = api_key
        logger.info(f"为 {ticker} 使用API密钥")
    else:
        logger.info(f"{ticker} 不需要API密钥")
    return headers

# 在 get_prices 函数中使用标准化日期函数
def get_prices(ticker: str, start_date: str | datetime, end_date: str | datetime, interval: str = "day", interval_multiplier: int = 1, force_refresh: bool = False) -> list[Price]:
    """
    从缓存或API获取价格数据。
    
    Args:
        ticker: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        interval: 时间间隔，可选值包括 "minute", "hour", "day", "week", "month"
        interval_multiplier: 间隔乘数，例如 interval="minute", interval_multiplier=5 表示5分钟间隔
        force_refresh: 是否允许从API获取数据，默认为False。设为False时只从缓存获取
    """
    logger.info(f"获取价格数据: {ticker}, 时间间隔: {interval}, 乘数: {interval_multiplier}, 开始日期: {start_date}, 结束日期: {end_date}")
    
    # 1. 标准化输入日期为 'YYYY-MM-DD' 格式的字符串
    normalized_local_start_date = normalize_date_string(start_date)
    if not normalized_local_start_date:
        logger.error(f"start_date '{start_date}' 标准化后为空或None。")
        raise ValueError("get_prices 需要一个有效的 start_date。")
        
    normalized_local_end_date = normalize_date_string(end_date)
    if not normalized_local_end_date:
        logger.error(f"end_date '{end_date}' 标准化后为空或None。")
        raise ValueError("get_prices 需要一个有效的 end_date。")

    # 2. 将标准化的本地日期字符串转换为API所需的UTC日期字符串
    api_utc_start_date = get_utc_date_for_api_start(normalized_local_start_date)
    api_utc_end_date = get_utc_date_for_api_end(normalized_local_end_date)
    logger.info(f"API UTC 日期范围: {api_utc_start_date} - {api_utc_end_date}")

    # 获取缓存锁
    cache_lock = _cache.get_cache_lock("prices", ticker, interval=interval, interval_multiplier=interval_multiplier)
    
    with cache_lock:
        # 先尝试从缓存获取数据
        cached_data = _cache.get_prices(ticker, interval=interval, interval_multiplier=interval_multiplier)
        
        if cached_data:
            logger.debug(f"找到缓存数据: {ticker}_{interval}_{interval_multiplier}, 记录数: {len(cached_data)}")
            
            # 按日期范围过滤缓存数据并转换为Price对象
            filtered_data = []
            for price in cached_data:
                # 提取日期部分进行比较
                price_date = normalize_date_string(price["time"])
                if api_utc_start_date <= price_date <= api_utc_end_date:
                    filtered_data.append(Price(**price))
            
            if filtered_data and not force_refresh:
                logger.info(f"从缓存获取到 {len(filtered_data)} 条价格记录")
                # 标准化时间戳
                for price in filtered_data:
                    price.time = normalize_timestamp(price.time)
                return filtered_data
        
        if not force_refresh:
            logger.warning(f"不允许API请求且缓存中无数据，返回空列表, API UTC 日期范围: {api_utc_start_date} - {api_utc_end_date}")
            return []
            
        # API请求部分
        headers = _get_headers(ticker)
    
        url = f"https://api.financialdatasets.ai/prices/?ticker={ticker}&interval={interval}&interval_multiplier={interval_multiplier}&start_date={api_utc_start_date}&end_date={api_utc_end_date}"
        logger.info(f"从API获取价格数据: {url}")
        
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            error_msg = f"获取数据错误: {ticker} - {response.status_code} - {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
        # 使用Pydantic模型解析响应
        price_response = PriceResponse(**response.json())
        prices = price_response.prices
    
        if not prices:
            logger.warning(f"未找到价格数据")
            return []
    
        # 标准化时间戳
        for price in prices:
            price.time = normalize_timestamp(price.time)
            
        # 添加最后更新时间戳
        price_dicts = [p.model_dump() for p in prices]
        for p in price_dicts:
            p['_last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 将结果缓存为字典
        _cache.set_prices(ticker, price_dicts, interval=interval, interval_multiplier=interval_multiplier)
        logger.info(f"从API获取到 {len(prices)} 条价格记录并已缓存")
        return prices


def get_financial_metrics(
    ticker: str,
    end_date: str | datetime | None,
    period: str = "ttm",
    limit: int = 100,
    force_refresh: bool = False
) -> list[FinancialMetrics]:
    """从缓存或API获取财务指标。
    
    Args:
        end_date: 本地时区的结束日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
    """
    # 1. 标准化输入日期为 'YYYY-MM-DD' 格式的字符串或 None
    normalized_local_end_date = normalize_date_string(end_date)
    # 2. 将标准化的本地日期字符串转换为API所需的UTC日期字符串
    api_utc_end_date = get_utc_date_for_api_end(normalized_local_end_date)
    
    cache_lock = _cache.get_cache_lock("financial_metrics", ticker)
    
    with cache_lock:
        cached_data = _cache.get_financial_metrics(ticker)
        filtered_data = []
        
        if cached_data:
            
            for metric in cached_data:
                if isinstance(metric, dict) and "report_period" in metric:
                    report_period = normalize_date_string(metric["report_period"])
                    # 如果 api_utc_end_date 为 None，表示没有上限，所有数据都应考虑
                    # 如果 report_period 为 None (不应发生但防御性检查)，则不比较
                    if report_period and (api_utc_end_date is None or report_period <= api_utc_end_date):
                        filtered_data.append(FinancialMetrics(**metric))
            
            filtered_data.sort(key=lambda x: x.report_period, reverse=True)
            if filtered_data and not force_refresh:
                logger.info(f"从缓存获取到 {len(filtered_data)} 条财务指标记录")
                return filtered_data[:limit]

        if not force_refresh:
            logger.warning(f"不允许API请求且缓存中无符合条件的财务指标数据 (end_date: {end_date} -> api_utc_end_date: {api_utc_end_date})，返回空列表")
            return []

        # 如果允许API请求，则从API获取
        headers = _get_headers(ticker)

        url_parts = [f"https://api.financialdatasets.ai/financial-metrics/?ticker={ticker}"]
        if api_utc_end_date: # 仅当 api_utc_end_date 不是 None 时才添加此参数
            url_parts.append(f"&report_period_lte={api_utc_end_date}")
        url_parts.append(f"&limit={limit}&period={period}")
        url = "".join(url_parts)
        logger.info(f"从API获取财务指标: {url}")
        
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise Exception(f"获取数据错误: {ticker} - {response.status_code} - {response.text}")

        # 使用Pydantic模型解析响应
        metrics_response = FinancialMetricsResponse(**response.json())
        financial_metrics = metrics_response.financial_metrics

        if not financial_metrics:
            logger.warning(f"未找到财务指标数据")
            return []

        # 将结果缓存为字典
        metrics_dicts = [m.model_dump() for m in financial_metrics]
        # 添加最后更新时间戳
        for m in metrics_dicts:
            m['_last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        _cache.set_financial_metrics(ticker, metrics_dicts)
        logger.info(f"从API获取到 {len(financial_metrics)} 条财务指标记录并已缓存")
        return financial_metrics


def search_line_items(
    ticker: str,
    line_items: list[str],
    end_date: str | datetime | None,
    period: str = "ttm",
    limit: int = 100,
    force_refresh: bool = False
) -> list[LineItem]:
    """从缓存或API获取财务项目。
    
    Args:
        end_date: 本地时区的结束日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
    """
    # 1. 标准化输入日期为 'YYYY-MM-DD' 格式的字符串或 None
    normalized_local_end_date = normalize_date_string(end_date)
    # 2. 将标准化的本地日期字符串转换为API所需的UTC日期字符串
    api_utc_end_date = get_utc_date_for_api_end(normalized_local_end_date)
    
    cache_lock = _cache.get_cache_lock("line_items", ticker)
    
    with cache_lock:
        cached_data = _cache.get_line_items(ticker)
        filtered_data = []
        
        if cached_data:
            
            for item in cached_data:
                if "report_period" in item:
                    # 检查是否包含所有请求的字段
                    has_all_fields = all(field.lower() in item for field in line_items)
                    report_period = normalize_date_string(item["report_period"])
                    if has_all_fields and report_period and (api_utc_end_date is None or report_period <= api_utc_end_date):
                        # 将字典转换为 LineItem 对象
                        filtered_data.append(LineItem(**item))
            
            filtered_data.sort(key=lambda x: x.report_period, reverse=True)
            
            if filtered_data and not force_refresh:
                logger.info(f"从缓存获取到 {len(filtered_data)} 条财务项目记录")
                return filtered_data[:limit]
        
        if not force_refresh:
            logger.warning(f"不允许API请求且缓存中无符合条件的财务项目数据 (end_date: {end_date} -> api_utc_end_date: {api_utc_end_date})，返回空列表。查询项为 {line_items}")
            return []

        # 如果允许API请求，则从API获取
        headers = _get_headers(ticker)

        url = "https://api.financialdatasets.ai/financials/search/line-items"
        logger.info(f"从API获取财务项目: {url}")

        body = {
            "tickers": [ticker],
            "line_items": line_items,
            "period": period,
            "limit": limit,
        }
        if api_utc_end_date: # 仅当 api_utc_end_date 不是 None 时才添加到 body
            body["end_date"] = api_utc_end_date
            
        response = requests.post(url, headers=headers, json=body)
        if response.status_code != 200:
            raise Exception(f"获取数据错误: {ticker} - {response.status_code} - {response.text}")
        data = response.json()
        response_model = LineItemResponse(**data)
        search_results = response_model.search_results
        if not search_results:
            logger.warning(f"未找到财务项目数据")
            return []

        # 为每个项目添加唯一键和时间戳
        line_items_to_cache = []
        for item in search_results:
            item_dict = item.model_dump()
            # # 创建一个基于报告期和所有请求指标的唯一键
            # item_indicators = "_".join(sorted(line_items))  # 对指标进行排序以确保一致性
            # item_dict["_unique_key"] = f"{item_dict.get('report_period')}_{item_indicators}"
            item_dict["_unique_key"] = item_dict.get("report_period", "")
            # 添加最后更新时间戳
            item_dict['_last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            line_items_to_cache.append(item_dict)
        
        _cache.set_line_items(ticker, line_items_to_cache)
        logger.info(f"从API获取到 {len(search_results)} 条财务项目记录并已缓存")
        
        return search_results[:limit]


def get_insider_trades(
    ticker: str,
    end_date: str | datetime | None,
    start_date: str | None = None,
    limit: int = 1000,
    force_refresh: bool = False
) -> list[InsiderTrade]:
    """从缓存或API获取内部交易数据。
    
    Args:
        end_date: 本地时区的结束日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
        start_date: 本地时区的开始日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
    """
    normalized_local_end_date = normalize_date_string(end_date)
    api_utc_end_date = get_utc_date_for_api_end(normalized_local_end_date)
    api_utc_start_date = get_utc_date_for_api_start(normalize_date_string(start_date)) if start_date else None
    
    cache_lock = _cache.get_cache_lock("insider_trades", ticker)
    
    with cache_lock:
        cached_data = _cache.get_insider_trades(ticker)
        filtered_data = []
        
        if cached_data:
            
            for trade in cached_data:
                if isinstance(trade, dict) and all(key in trade for key in ["filing_date", "transaction_date"]):
                    trade_date = trade.get("transaction_date") or trade["filing_date"]
                    trade_date = normalize_date_string(trade_date)
                    if trade_date: #确保 trade_date 有效
                        start_condition = api_utc_start_date is None or trade_date >= api_utc_start_date
                        end_condition = api_utc_end_date is None or trade_date <= api_utc_end_date
                        if start_condition and end_condition:
                            filtered_data.append(InsiderTrade(**trade))
            
            filtered_data.sort(key=lambda x: x.transaction_date or x.filing_date, reverse=True)
            if filtered_data and not force_refresh:
                logger.info(f"从缓存获取到 {len(filtered_data)} 条内部交易记录")
                return filtered_data[:limit] # Apply limit if returning from cache

        if not force_refresh:
            logger.warning(f"不允许API请求且缓存中无符合条件的内部交易数据 (start_date: {start_date} -> {api_utc_start_date}, end_date: {end_date} -> {api_utc_end_date})，返回空列表")
            return []
    
        # 如果允许API请求，则从API获取
        headers = _get_headers(ticker)

        all_trades = []
        # Use the API-ready UTC date for pagination; if None, API might not support it or means "no upper bound"
        # For pagination, if api_utc_end_date is None, we might need to default to today's UTC date or handle differently.
        # Assuming API handles missing filing_date_lte or interprets it as "latest".
        # If API requires filing_date_lte, and api_utc_end_date is None, this logic needs adjustment.
        current_api_end_date_for_pagination = api_utc_end_date 
        
        while True:
            url = f"https://api.financialdatasets.ai/insider-trades/?ticker={ticker}"
            if current_api_end_date_for_pagination: # Only add if not None
                url += f"&filing_date_lte={current_api_end_date_for_pagination}"
            if api_utc_start_date:
                url += f"&filing_date_gte={api_utc_start_date}"
            url += f"&limit={limit}"
            
            logger.info(f"从API获取内部交易数据: {url}")
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                raise Exception(f"获取数据错误: {ticker} - {response.status_code} - {response.text}")
            
            data = response.json()
            response_model = InsiderTradeResponse(**data)
            insider_trades = response_model.insider_trades
            
            if not insider_trades:
                break
                
            all_trades.extend(insider_trades)
            
            # Stop if:
            # 1. We are not paginating towards a start_date (api_utc_start_date is None) AND we got less than a full page (means no more data)
            # 2. We got less than a full page (means no more data in this direction)
            if (api_utc_start_date is None and len(insider_trades) < limit) or len(insider_trades) < limit:
                break
                
            # 更新结束日期为当前批次中最早的申报日期，用于下一次迭代
            current_api_end_date_for_pagination = normalize_date_string(min(trade.filing_date for trade in insider_trades))
            
            # 如果我们已经达到或超过了开始日期，可以停止
            if api_utc_start_date and current_api_end_date_for_pagination <= api_utc_start_date:
                break

        if not all_trades: # After pagination loop
            logger.warning(f"未找到内部交易数据")
            return []

        # 创建一个复合键来确保正确合并
        trades_to_cache = []
        for trade in all_trades:
            trade_dict = trade.model_dump()
            # 添加一个复合键字段用于去重
            trade_dict["_unique_key"] = trade_dict.get("id", f"{trade_dict.get('transaction_date', '')}_{trade_dict.get('filing_date', '')}_{trade_dict.get('insider_name', '')}")
            # 添加最后更新时间戳
            trade_dict['_last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            trades_to_cache.append(trade_dict)
        _cache.set_insider_trades(ticker, trades_to_cache)
        logger.info(f"从API获取到 {len(all_trades)} 条内部交易记录并已缓存")
        
        return all_trades[:limit] # Apply overall limit if pagination fetched more


def get_company_news(
    ticker: str,
    end_date: str | datetime | None,
    start_date: str | None = None,
    limit: int = 1000,
    force_refresh: bool = False
) -> list[CompanyNews]:
    """从缓存或API获取公司新闻。
    
    Args:
        end_date: 本地时区的结束日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
        start_date: 本地时区的开始日期，可以是 'YYYY-MM-DD' 字符串、datetime 对象或 None。
    """
    normalized_local_start_date = normalize_date_string(start_date) if start_date else None
    api_utc_start_date = get_utc_date_for_api_start(normalized_local_start_date) if normalized_local_start_date else None
    api_utc_end_date = get_utc_date_for_api_end(normalize_date_string(end_date))

    cache_lock = _cache.get_cache_lock("company_news", ticker)
    
    with cache_lock:
        cached_data = _cache.get_company_news(ticker)
        filtered_data = []
        
        if cached_data:
            
            for news in cached_data:
                if isinstance(news, dict) and "date" in news:
                    news_date = normalize_date_string(news["date"])
                    if news_date: #确保 news_date 有效
                        start_condition = api_utc_start_date is None or news_date >= api_utc_start_date
                        end_condition = api_utc_end_date is None or news_date <= api_utc_end_date
                        if start_condition and end_condition:
                            filtered_data.append(CompanyNews(**news))
            
            filtered_data.sort(key=lambda x: x.date, reverse=True)
            if filtered_data and not force_refresh:
                logger.info(f"从缓存获取到 {len(filtered_data)} 条公司新闻")
                return filtered_data[:limit] # Apply limit

        if not force_refresh:
            logger.warning(f"不允许API请求且缓存中无符合条件的公司新闻数据 (start_date: {start_date} -> {api_utc_start_date}, end_date: {end_date} -> {api_utc_end_date})，返回空列表")
            return []

        # 如果允许API请求，则从API获取
        headers = _get_headers(ticker)

        all_news = []
        # Similar pagination consideration for api_utc_end_date being None
        current_api_end_date_for_pagination = api_utc_end_date 
        
        while True:
            url = f"https://api.financialdatasets.ai/news/?ticker={ticker}"
            if current_api_end_date_for_pagination: # Only add if not None
                url += f"&end_date={current_api_end_date_for_pagination}"
            if api_utc_start_date:
                url += f"&start_date={api_utc_start_date}"
            url += f"&limit={limit}"
            
            logger.info(f"从API获取公司新闻: {url}")
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                raise Exception(f"获取数据错误: {ticker} - {response.status_code} - {response.text}")
            
            data = response.json()
            response_model = CompanyNewsResponse(**data)
            company_news = response_model.news
            
            if not company_news:
                break
                
            all_news.extend(company_news)
            
            # Stop if: (similar logic to insider trades)
            if (api_utc_start_date is None and len(company_news) < limit) or len(company_news) < limit:
                break
                
            # 更新结束日期为当前批次中最早的日期，用于下一次迭代
            current_api_end_date_for_pagination = normalize_date_string(min(news.date for news in company_news))
            
            # 如果我们已经达到或超过了开始日期，可以停止
            if api_utc_start_date and current_api_end_date_for_pagination <= api_utc_start_date:
                break

        # After pagination loop
        if not all_news: 
            logger.warning(f"未找到公司新闻")
            return []

        # 为每条新闻添加唯一键
        news_to_cache = []
        for news in all_news:
            news_dict = news.model_dump()
            # 添加一个复合键字段用于去重
            news_dict["_unique_key"] = news_dict.get("id", f"{news_dict.get('date', '')}_{news_dict.get('title', '')}"[:100])
            # 添加最后更新时间戳
            news_dict['_last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            news_to_cache.append(news_dict)
        
        _cache.set_company_news(ticker, news_to_cache)
        logger.info(f"从API获取到 {len(all_news)} 条公司新闻并已缓存")
        
        return all_news[:limit] # Apply overall limit


def get_market_cap(
    ticker: str,
    end_date: str | datetime | None,
) -> float | None:
    """从API获取市值。"""
    # 这个方法依赖于 get_financial_metrics，所以会继承其缓存机制
    financial_metrics = get_financial_metrics(ticker, end_date)
    if not financial_metrics:
        return None
    market_cap = financial_metrics[0].market_cap
    if not market_cap:
        return None

    return market_cap


def prices_to_df(prices: list[Price]) -> pd.DataFrame:
    """将价格转换为DataFrame。"""
    df = pd.DataFrame([p.model_dump() for p in prices])
    df["Date"] = pd.to_datetime(df["time"])
    df.set_index("Date", inplace=True)
    numeric_cols = ["open", "close", "high", "low", "volume"]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df.sort_index(inplace=True)
    return df


def get_price_data(ticker, start_date, end_date, interval="day", multiplier=1, force_refresh=False):
    """获取指定时间范围内的价格数据"""
    prices = get_prices(ticker, start_date, end_date, interval=interval, interval_multiplier=multiplier, force_refresh=force_refresh)
    
    return prices_to_df(prices)

def get_start_date_for_lookback(end_date_input: str | datetime, years: int) -> str:
    """Calculates a start date string for a given lookback period in years."""
    
    # 使用 normalize_date_string 来标准化输入日期。
    # 该函数能处理 str 和 datetime 对象，并目标返回 'YYYY-MM-DD' 格式的字符串。
    normalized_date_str = normalize_date_string(end_date_input)
    
    # 检查 normalize_date_string 是否返回了有效的、非空的字符串
    if not isinstance(normalized_date_str, str) or not normalized_date_str:
        logger.error(f"normalize_date_string 未能从输入生成有效的日期字符串: {end_date_input}. 得到: {normalized_date_str}")
        raise ValueError(f"提供的结束日期无效或为空: {end_date_input}")

    try:
        # 将 'YYYY-MM-DD' 格式的字符串解析为 datetime 对象
        end_date_dt = datetime.strptime(normalized_date_str, '%Y-%m-%d')
    except ValueError as e:
        # 处理 normalized_date_str 不是 'YYYY-MM-DD' 格式的情况
        # (例如，如果 normalize_date_string 在出错时返回了无法解析的原始字符串)
        logger.error(f"解析日期字符串 '{normalized_date_str}' (来自输入 '{end_date_input}') 到 datetime 失败: {e}")
        raise ValueError(f"结束日期 '{end_date_input}' (标准化为 '{normalized_date_str}') 无法解析。期望格式为 YYYY-MM-DD。") from e
        
    start_date_dt = end_date_dt - timedelta(days=365 * years)
    return start_date_dt.strftime("%Y-%m-%d")