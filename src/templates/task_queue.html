<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务队列</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .task-card {
            transition: all 0.3s ease;
        }
        .task-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .task-status-active {
            color: #198754;
        }
        .task-status-paused {
            color: #fd7e14;
        }
        .task-status-completed {
            color: #0d6efd;
        }
        .task-status-error {
            color: #dc3545;
        }
        .action-buy, .action-cover {
            color: green;
            font-weight: bold;
        }
        .action-sell, .action-short {
            color: red;
            font-weight: bold;
        }
        .action-hold {
            color: gray;
            font-weight: bold;
        }
        .btn-reason {
            color: #6c757d;
            background-color: transparent;
            border-color: #6c757d;
            opacity: 0.7;
            font-size: 0.9em;
            margin-right: 5px;
        }
        .btn-reason:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 1;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: normal;
        }
        
        /* 导航栏样式 */
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
            background-color: white !important;
            padding: 0.8rem 1rem;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.4rem;
        }
        
        .nav-link {
            color: #6c757d !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #2c3e50 !important;
            background-color: #f8f9fa;
        }
        
        .nav-link.active {
            color: #007bff !important;
            background-color: rgba(0,123,255,.1);
        }
        
        .user-info .btn-outline-secondary {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }
        
        .user-info .btn-outline-secondary:hover,
        .user-info .btn-outline-secondary:focus {
            color: #2c3e50;
            background-color: #f8f9fa;
            box-shadow: none;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            border-radius: 0.5rem;
        }
        
        .dropdown-item {
            padding: 0.5rem 1.5rem;
            color: #6c757d;
        }
        
        .dropdown-item:hover {
            color: #2c3e50;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light mb-4 rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="bi bi-graph-up"></i> AI Fund
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="bi bi-house"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'history' %}active{% endif %}" href="{{ url_for('history') }}">
                                <i class="bi bi-clock-history"></i> 历史记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'task_queue' %}active{% endif %}" href="{{ url_for('task_queue') }}">
                                <i class="bi bi-list-task"></i> 任务队列
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'data_monitor' %}active{% endif %}" href="{{ url_for('data_monitor') }}">
                                <i class="bi bi-database-gear"></i> 数据监控
                            </a>
                        </li>
                    </ul>
                    <div class="user-info">
                        {% if user and not config.get('dev_mode', False) %}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i> {{ user.username }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> 退出登录
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- <h1 class="mb-4">任务队列</h1> -->
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">定时任务列表</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if tasks|length > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>股票代码</th>
                                <th>执行频率</th>
                                <th>下次执行</th>
                                <th>状态</th>
                                <th>操作</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {# 首先显示运行中的任务 #}
                            {% for task in tasks|sort(attribute='status', reverse=true) %}
                                {% if task.status == 'active' %}
                                <tr>
                                    <td data-label="任务名称">{{ task.name or '未命名任务' }}</td>
                                    <td data-label="股票代码">{{ task.tickers }}</td>
                                    <td data-label="执行频率">{{ task.execution_interval }}</td>
                                    <td data-label="下次执行">{{ task.next_run }}</td>
                                    <td data-label="状态">
                                        <span class="badge bg-success">运行中</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning pause-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-pause-fill"></i>
                                            </button>
                                            <button class="btn btn-outline-danger delete-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <button class="btn btn-outline-info view-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('task_detail', task_id=task.id) }}" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="bi bi-file-earmark-text"></i> 查看详情
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                            {# 然后显示其他状态的任务 #}
                            {% for task in tasks|sort(attribute='status', reverse=true) %}
                                {% if task.status != 'active' %}
                                <tr>
                                    <td data-label="任务名称">{{ task.name or '未命名任务' }}</td>
                                    <td data-label="股票代码">{{ task.tickers }}</td>
                                    <td data-label="执行频率">{{ task.execution_interval }}</td>
                                    <td data-label="下次执行">{{ task.next_run }}</td>
                                    <td data-label="状态">
                                        {% if task.status == 'paused' %}
                                        <span class="badge bg-warning">已暂停</span>
                                        {% elif task.status == 'completed' %}
                                        <span class="badge bg-primary">已完成</span>
                                        {% elif task.status == 'error' %}
                                        <span class="badge bg-danger">错误</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if task.status == 'paused' %}
                                            <button class="btn btn-outline-success resume-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-play-fill"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-danger delete-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <button class="btn btn-outline-info view-task-btn" data-task-id="{{ task.id }}">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('task_detail', task_id=task.id) }}" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="bi bi-file-earmark-text"></i> 查看详情
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x" style="font-size: 3rem; color: #6c757d;"></i>
                    <h5 class="mt-3">暂无定时任务</h5>
                    <p class="text-muted">您可以在交易页面创建定时执行的任务</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary mt-2">创建任务</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1" aria-labelledby="taskDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taskDetailModalLabel">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="task-detail-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/task_queue.js') }}"></script>
</body>
</html>