<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据监控</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .data-card {
            transition: all 0.3s ease;
        }
        .data-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: normal;
        }
        
        /* 内部交易数据颜色 */
        .positive-shares {
            color: #198754 !important; /* 绿色 - 添加!important确保优先级 */
            font-weight: 500;
        }
        
        .negative-shares {
            color: #dc3545 !important; /* 红色 - 添加!important确保优先级 */
            font-weight: 500;
        }
        
        /* 导航栏样式 */
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
            background-color: white !important;
            padding: 0.8rem 1rem;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.4rem;
        }
        
        .nav-link {
            color: #6c757d !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #2c3e50 !important;
            background-color: #f8f9fa;
        }
        
        .nav-link.active {
            color: #007bff !important;
            background-color: rgba(0,123,255,.1);
        }
        
        .user-info .btn-outline-secondary {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }
        
        .user-info .btn-outline-secondary:hover,
        .user-info .btn-outline-secondary:focus {
            color: #2c3e50;
            background-color: #f8f9fa;
            box-shadow: none;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            border-radius: 0.5rem;
        }
        
        .dropdown-item {
            padding: 0.5rem 1.5rem;
            color: #6c757d;
        }
        
        .dropdown-item:hover {
            color: #2c3e50;
            background-color: #f8f9fa;
        }
        
        .data-table {
            font-size: 0.9rem;
        }
        
        .data-table th {
            background-color: #f8f9fa;
        }
        
        .refresh-btn {
            margin-bottom: 20px;
        }
        
        .loading-spinner {
            display: none;
            margin-left: 10px;
        }
        
        .data-section {
            margin-bottom: 30px;
        }
        
        .data-section h3 {
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        /* 表格内部滚动样式 */
        .scrollable-table {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .scrollable-table thead th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light mb-4 rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="bi bi-graph-up"></i> AI Fund
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="bi bi-house"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'history' %}active{% endif %}" href="{{ url_for('history') }}">
                                <i class="bi bi-clock-history"></i> 历史记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'task_queue' %}active{% endif %}" href="{{ url_for('task_queue') }}">
                                <i class="bi bi-list-task"></i> 任务队列
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'data_monitor' %}active{% endif %}" href="{{ url_for('data_monitor') }}">
                                <i class="bi bi-database-gear"></i> 数据监控
                            </a>
                        </li>
                    </ul>
                    <div class="user-info">
                        {% if user and not config.get('dev_mode', False) %}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i> {{ user.username if user.username else session.get('username', 'User') }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> 退出登录
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>

    </div>

    <div class="container">
        <h1 class="mb-4">数据监控</h1>
        
        <div class="alert alert-info" role="alert">
            <i class="bi bi-info-circle-fill me-2"></i>
            此页面显示定时数据抓取任务的执行情况和最新数据。您可以查看各类数据的最新状态，并手动触发数据刷新。
        </div>
        
        <!-- 数据抓取状态和定时任务配置 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">数据抓取任务状态</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>后台调度器状态:</strong> <span id="scheduler-status" class="badge bg-success">运行中</span></p>
                        <p><strong>监控股票:</strong> <span id="monitored-stocks">{{ tickers|join(', ') }}</span></p>
                        <p><strong>市场类型:</strong> <span id="market-type">{{ market }}</span></p>
                        <p><strong>最后刷新时间:</strong> <span id="last-refresh-time" class="timestamp">{{ last_refresh_time }}</span></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">定时任务配置</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>价格数据刷新:</strong> 市场开放时每5分钟</p>
                        <p><strong>财务数据刷新:</strong> 每日{{ preload_hour }}:00 ({{ timezone }})</p>
                        <p><strong>新闻和内部交易刷新:</strong> 每4小时一次</p>
                        <button id="refresh-all-data" class="btn btn-primary refresh-btn">
                            <i class="bi bi-arrow-clockwise me-2"></i>立即刷新所有数据
                            <span class="spinner-border spinner-border-sm loading-spinner" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 股票选择Tab -->
        <ul class="nav nav-tabs mb-4" id="stockTabs" role="tablist">
            {% for ticker in tickers %}
            <li class="nav-item" role="presentation">
                <button class="nav-link {% if loop.first %}active{% endif %}" id="{{ ticker }}-tab" data-bs-toggle="tab" data-bs-target="#{{ ticker }}-data" type="button" role="tab" aria-controls="{{ ticker }}-data" aria-selected="{% if loop.first %}true{% else %}false{% endif %}">{{ ticker }}</button>
            </li>
            {% endfor %}
        </ul>
        
        <div class="tab-content" id="stockTabsContent">
            {% for ticker in tickers %}
            <div class="tab-pane fade {% if loop.first %}show active{% endif %}" id="{{ ticker }}-data" role="tabpanel" aria-labelledby="{{ ticker }}-tab">
                <h2 class="mb-3">{{ ticker }} 数据</h2>
                
                <!-- 价格数据 -->
                <div class="data-section">
                    <h3><i class="bi bi-graph-up me-2"></i>价格数据</h3>
                    <div class="row">
                        <div class="col-12">
                            <div class="card data-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title">最新价格数据</h5>
                                        <button class="btn btn-outline-primary btn-sm refresh-price-data" data-ticker="{{ ticker }}">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                            <span class="spinner-border spinner-border-sm loading-spinner" role="status" aria-hidden="true"></span>
                                        </button>
                                    </div>
                                    <div class="scrollable-table">
                                        <table class="table table-striped table-hover data-table">
                                            <thead>
                                                <tr>
                                                    <th>日期</th>
                                                    <th>开盘价</th>
                                                    <th>最高价</th>
                                                    <th>最低价</th>
                                                    <th>收盘价</th>
                                                    <th>成交量</th>
                                                    <th>更新时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if ticker in price_data %}
                                                    {% for data in price_data[ticker] %}
                                                    <tr>
                                                        <td>{{ data.time }}</td>
                                                        <td>{{ data.open }}</td>
                                                        <td>{{ data.high }}</td>
                                                        <td>{{ data.low }}</td>
                                                        <td>{{ data.close }}</td>
                                                        <td>{{ "{:,.0f}".format(data.volume|float) if data.volume else data.volume }}</td>
                                                        <td class="timestamp">{{ data._last_updated }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="7" class="text-center">暂无数据</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 财务指标数据 -->
                <div class="data-section">
                    <h3><i class="bi bi-bar-chart me-2"></i>财务指标数据</h3>
                    <div class="row">
                        <div class="col-12">
                            <div class="card data-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title">最新财务指标</h5>
                                        <button class="btn btn-outline-primary btn-sm refresh-financial-data" data-ticker="{{ ticker }}">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                            <span class="spinner-border spinner-border-sm loading-spinner" role="status" aria-hidden="true"></span>
                                        </button>
                                    </div>
                                    <div class="scrollable-table">
                                        <table class="table table-striped table-hover data-table">
                                            <thead>
                                                <tr>
                                                    <th>报告期</th>
                                                    <th>市值</th>
                                                    <th>每股收益</th>
                                                    <th>市盈率</th>
                                                    <th>市净率</th>
                                                    <th>市销率</th>
                                                    <th>更新时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if ticker in financial_data %}
                                                    {% for data in financial_data[ticker] %}
                                                    <tr>
                                                        <td>{{ data.report_period }}</td>
                                                        <td>{{ "{:,.0f}".format(data.market_cap|float) if data.market_cap else data.market_cap }}</td>
                                                        <td>{{ "%.3f"|format(data.earnings_per_share|float) if data.earnings_per_share else data.earnings_per_share }}</td>
                                                        <td>{{ "%.3f"|format(data.price_to_earnings_ratio|float) if data.price_to_earnings_ratio else data.price_to_earnings_ratio }}</td>
                                                        <td>{{ "%.3f"|format(data.price_to_book_ratio|float) if data.price_to_book_ratio else data.price_to_book_ratio }}</td>
                                                        <td>{{ "%.3f"|format(data.price_to_sales_ratio|float) if data.price_to_sales_ratio else data.price_to_sales_ratio }}</td>
                                                        <td class="timestamp">{{ data._last_updated }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="7" class="text-center">暂无数据</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 内部交易数据 -->
                <div class="data-section">
                    <h3><i class="bi bi-person-badge me-2"></i>内部交易数据</h3>
                    <div class="row">
                        <div class="col-12">
                            <div class="card data-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title">最新内部交易</h5>
                                        <button class="btn btn-outline-primary btn-sm refresh-insider-data" data-ticker="{{ ticker }}">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                            <span class="spinner-border spinner-border-sm loading-spinner" role="status" aria-hidden="true"></span>
                                        </button>
                                    </div>
                                    <div class="scrollable-table">
                                        <table class="table table-striped table-hover data-table">
                                            <thead>
                                                <tr>
                                                    <th>交易日期</th>
                                                    <th>内部人</th>
                                                    <th>职位</th>
                                                    <th>股数</th>
                                                    <th>价格</th>
                                                    <th>交易前持股</th>
                                                    <th>交易后持股</th>
                                                    <th>更新时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if ticker in insider_data %}
                                                    {% for data in insider_data[ticker] %}
                                                    <tr>
                                                        <td>{{ data.transaction_date }}</td>
                                                        <td>{{ data.name }}</td>
                                                        <td>{{ data.title }}</td>
                                                        <td class="{% if data.transaction_shares|float > 0 %}positive-shares{% elif data.transaction_shares|float < 0 %}negative-shares{% endif %}">
                                                            {{ data.transaction_shares }}
                                                        </td>
                                                        <td>{{ data.transaction_price_per_share }}</td>
                                                        <td>{{ "{:,.0f}".format(data.shares_owned_before_transaction|float) if data.shares_owned_before_transaction else data.shares_owned_before_transaction }}</td>
                                                        <td>{{ "{:,.0f}".format(data.shares_owned_after_transaction|float) if data.shares_owned_after_transaction else data.shares_owned_after_transaction }}</td>
                                                        <td class="timestamp">{{ data._last_updated }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="8" class="text-center">暂无数据</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 新闻数据 -->
                <div class="data-section">
                    <h3><i class="bi bi-newspaper me-2"></i>新闻数据</h3>
                    <div class="row">
                        <div class="col-12">
                            <div class="card data-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title">最新新闻</h5>
                                        <button class="btn btn-outline-primary btn-sm refresh-news-data" data-ticker="{{ ticker }}">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                            <span class="spinner-border spinner-border-sm loading-spinner" role="status" aria-hidden="true"></span>
                                        </button>
                                    </div>
                                    <div class="scrollable-table">
                                        <table class="table table-striped table-hover data-table">
                                            <thead>
                                                <tr>
                                                    <th>日期</th>
                                                    <th>标题</th>
                                                    <th>来源</th>
                                                    <th>更新时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if ticker in news_data %}
                                                    {% for data in news_data[ticker] %}
                                                    <tr>
                                                        <td>{{ data.date }}</td>
                                                        <td>{{ data.title }}</td>
                                                        <td>{{ data.source }}</td>
                                                        <td class="timestamp">{{ data._last_updated }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">暂无数据</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 刷新所有数据
            $('#refresh-all-data').click(function() {
                const button = $(this);
                const spinner = button.find('.loading-spinner');
                
                button.prop('disabled', true);
                spinner.show();
                
                $.ajax({
                    url: '/api/data/refresh_all',
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            alert('数据刷新任务已启动，请稍后刷新页面查看最新数据。');
                            // 启动轮询检查最后刷新时间
                            pollLastRefreshTime();
                        } else {
                            alert('错误: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试。');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        spinner.hide();
                    }
                });
            });
            
            // 刷新特定股票的价格数据
            $('.refresh-price-data').click(function() {
                const ticker = $(this).data('ticker');
                refreshData($(this), 'price', ticker);
            });
            
            // 刷新特定股票的财务数据
            $('.refresh-financial-data').click(function() {
                const ticker = $(this).data('ticker');
                refreshData($(this), 'financial', ticker);
            });
            
            // 刷新特定股票的新闻数据
            $('.refresh-news-data').click(function() {
                const ticker = $(this).data('ticker');
                refreshData($(this), 'news', ticker);
            });
            
            // 刷新特定股票的内部交易数据
            $('.refresh-insider-data').click(function() {
                const ticker = $(this).data('ticker');
                refreshData($(this), 'insider', ticker);
            });
            
            function refreshData(button, dataType, ticker) {
                const spinner = button.find('.loading-spinner');
                
                button.prop('disabled', true);
                spinner.show();
                
                $.ajax({
                    url: '/api/data/refresh',
                    type: 'POST',
                    data: { 
                        data_type: dataType,
                        ticker: ticker 
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('数据刷新任务已启动，请稍后刷新页面查看最新数据。');
                            // 启动轮询检查最后刷新时间
                            pollLastRefreshTime();
                        } else {
                            alert('错误: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试。');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        spinner.hide();
                    }
                });
            }
            
            // 保存当前活动的Tab到localStorage
            $('#stockTabs button').on('shown.bs.tab', function (e) {
                localStorage.setItem('activeStockTab', $(e.target).attr('id'));
            });
            
            // 从localStorage恢复上次活动的Tab
            var activeTab = localStorage.getItem('activeStockTab');
            if (activeTab) {
                $('#' + activeTab).tab('show');
            }
        });

        // 轮询检查最后刷新时间
        function pollLastRefreshTime() {
            let pollCount = 0;
            const maxPolls = 60; // 最多轮询60次（约10分钟）
            
            const pollInterval = setInterval(function() {
                $.ajax({
                    url: '/api/data/last_refresh_time',
                    type: 'GET',
                    success: function(response) {
                        if (response.success && response.last_refresh_time) {
                            // 更新页面上的最后刷新时间
                            $('#last-refresh-time').text(response.last_refresh_time);
                            clearInterval(pollInterval);
                        }
                    },
                    complete: function() {
                        pollCount++;
                        if (pollCount >= maxPolls) {
                            clearInterval(pollInterval);
                        }
                    }
                });
            }, 10000); // 每10秒检查一次
        }
    </script>
</body>
</html>
