<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .action-buy, .action-cover {
            color: green;
            font-weight: bold;
        }
        .action-sell, .action-short {
            color: red;
            font-weight: bold;
        }
        .action-hold {
            color: gray;
            font-weight: bold;
        }
        .model-name, .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: normal;
        }
        .signal-bullish {
            color: green;
            font-weight: bold;
        }
        .signal-bearish {
            color: red;
            font-weight: bold;
        }
        .signal-neutral {
            color: orange;
            font-weight: bold;
        }
        .btn-detail {
            color: #6c757d;
            background-color: transparent;
            border-color: #6c757d;
            opacity: 0.7;
            font-size: 0.9em;
        }
        .btn-detail:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 1;
        }
        .btn-reason {
            color: #6c757d;
            background-color: transparent;
            border-color: #6c757d;
            opacity: 0.7;
            font-size: 0.9em;
            margin-right: 5px;
        }
        .btn-reason:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 1;
        }
        .tooltip-inner {
            max-width: 350px;
            text-align: left;
        }
        
        /* 导航栏样式 */
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
            background-color: white !important;
            padding: 0.8rem 1rem;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.4rem;
        }
        
        .nav-link {
            color: #6c757d !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #2c3e50 !important;
            background-color: #f8f9fa;
        }
        
        .nav-link.active {
            color: #007bff !important;
            background-color: rgba(0,123,255,.1);
        }
        
        .user-info .btn-outline-secondary {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }
        
        .user-info .btn-outline-secondary:hover,
        .user-info .btn-outline-secondary:focus {
            color: #2c3e50;
            background-color: #f8f9fa;
            box-shadow: none;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            border-radius: 0.5rem;
        }
        
        .dropdown-item {
            padding: 0.5rem 1.5rem;
            color: #6c757d;
        }
        
        .dropdown-item:hover {
            color: #2c3e50;
            background-color: #f8f9fa;
        }
        
        /* 筛选区域样式 */
        .filter-container {
            padding: 0.5rem 0;
            margin-bottom: 0.75rem;
            border-bottom: 1px solid #eee;
        }
        
        .form-select-sm {
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
        }
        
        /* 确保表格头部紧凑 */
        .table thead th {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light mb-4 rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="bi bi-graph-up"></i> AI Fund
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="bi bi-house"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'history' %}active{% endif %}" href="{{ url_for('history') }}">
                                <i class="bi bi-clock-history"></i> 历史记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'task_queue' %}active{% endif %}" href="{{ url_for('task_queue') }}">
                                <i class="bi bi-list-task"></i> 任务队列
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'data_monitor' %}active{% endif %}" href="{{ url_for('data_monitor') }}">
                                <i class="bi bi-database-gear"></i> 数据监控
                            </a>
                        </li>
                    </ul>
                    <div class="user-info">
                        {% if user and not config.get('dev_mode', False) %}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i> {{ user.username }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> 退出登录
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>
       
       <!-- <h1 class="mb-4">历史记录</h1> -->
        
        <!-- 添加标签页导航 -->
        <ul class="nav nav-tabs mb-3" id="historyTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trading-tab" data-bs-toggle="tab" data-bs-target="#trading-records" type="button" role="tab" aria-controls="trading-records" aria-selected="false">
                    交易记录
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="backtest-tab" data-bs-toggle="tab" data-bs-target="#backtest-records" type="button" role="tab" aria-controls="backtest-records" aria-selected="false">
                    回测记录
                </button>
            </li>
        </ul>
        
        <!-- 标签页内容 -->
        <div class="tab-content" id="historyTabsContent">
            <!-- 交易记录 -->
            <div class="tab-pane fade" id="trading-records" role="tabpanel" aria-labelledby="trading-tab">
                <div class="card">
                    <div class="card-body">
                        {% set trading_records = records|selectattr('type', 'equalto', 'trading')|list %}
                        {% if trading_records %}
                        
                        <!-- 添加股票筛选功能 -->
                        <div class="filter-container">
                            <div class="d-flex align-items-center">
                                <label for="trading-stock-filter" class="form-label me-2 mb-0">按股票筛选:</label>
                                <select class="form-select form-select-sm me-2" id="trading-stock-filter" style="width: auto; min-width: 150px;">
                                    <option value="all">全部股票</option>
                                    {% set all_tickers = [] %}
                                    {% for record in trading_records %}
                                        {% for ticker in record.tickers %}
                                            {% if ticker not in all_tickers %}
                                                {% set _ = all_tickers.append(ticker) %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                    {% for ticker in all_tickers|sort %}
                                        <option value="{{ ticker }}">{{ ticker }}</option>
                                    {% endfor %}
                                </select>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="trading-filter-reset">重置</button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="trading-records-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>股票</th>
                                        <th>操作</th>
                                        <th>数量</th>
                                        <th>信心度</th>
                                        <th>决策理由</th>
                                        <th>分析师信号</th>
                                        <th>模型</th>
                                        <th>分析师</th>
                                        <th>详情</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in trading_records %}
                                    <tr data-tickers="{{ ','.join(record.tickers) }}">
                                        <td><span class="timestamp">{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</span></td>
                                        <td>{{ ', '.join(record.tickers) }}</td>
                                        <td>
                                            {% if record.decisions %}
                                                {% for ticker, decision in record.decisions.items() %}
                                                    {% if decision.action == "buy" %}
                                                    <span class="action-buy">买入</span>
                                                    {% elif decision.action == "sell" %}
                                                    <span class="action-sell">卖出</span>
                                                    {% elif decision.action == "short" %}
                                                    <span class="action-short">做空</span>
                                                    {% elif decision.action == "cover" %}
                                                    <span class="action-cover">回补</span>
                                                    {% else %}
                                                    <span class="action-hold">持有</span>
                                                    {% endif %}
                                                    {% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <!-- 其余列与全部记录相同 -->
                                        <td>
                                            {% if record.decisions %}
                                                {% for ticker, decision in record.decisions.items() %}
                                                    {{ decision.quantity }}
                                                    {% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.decisions %}
                                                {% for ticker, decision in record.decisions.items() %}
                                                    {{ decision.confidence }}%
                                                    {% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.decisions %}
                                                {% for ticker, decision in record.decisions.items() %}
                                                    {% if decision.reasoning %}
                                                            <button type="button" 
                                                                        class="btn btn-sm btn-reason" 
                                                                        data-bs-toggle="popover" 
                                                                        data-bs-trigger="click"
                                                                        data-bs-html="true"
                                                                        data-bs-content="{{ decision.reasoning }}">
                                                                        {{ ticker }}
                                                                    </button>
                                                    {% endif %}
                                                    {% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.analyst_signals %}
                                                {% for ticker in record.tickers %}
                                                    <button type="button" 
                                                            class="btn btn-sm btn-reason" 
                                                            data-bs-toggle="popover" 
                                                            data-bs-trigger="click"
                                                            data-bs-html="true"
                                                            data-bs-content="
                                                            {% for analyst, signals in record.analyst_signals.items() %}
                                                                {% if ticker in signals %}
                                                                <strong>{{ analyst_names.get(analyst, analyst) }}</strong>: 
                                                                {% if signals[ticker].signal == 'bullish' %}
                                                                <span class='signal-bullish'>看涨</span>
                                                                {% elif signals[ticker].signal == 'bearish' %}
                                                                <span class='signal-bearish'>看跌</span>
                                                                {% else %}
                                                                <span class='signal-neutral'>中性</span>
                                                                {% endif %}
                                                                ({{ signals[ticker].confidence }}%)
                                                                <br>
                                                                {% endif %}
                                                            {% endfor %}
                                                            ">
                                                            {{ ticker }}
                                                    </button>
                                                    {% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td><span class="model-name">{{ record.model_choice }}</span></td>
                                        <td>
                                            {% if record.selected_analysts %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-reason" 
                                                        data-bs-toggle="popover" 
                                                        data-bs-trigger="click"
                                                        data-bs-html="true"
                                                        data-bs-content="
                                                        {% for analyst in record.selected_analysts %}
                                                            {% set analyst_key = analyst %}
                                                            {% if not analyst.endswith('_agent') %}
                                                                {% if analyst in ['fundamentals_analyst', 'sentiment_analyst', 'valuation_analyst'] %}
                                                                    {% set analyst_key = analyst.replace('_analyst', '_agent') %}
                                                                {% else %}
                                                                    {% set analyst_key = analyst + '_agent' %}
                                                                {% endif %}
                                                            {% endif %}
                                                            {{ analyst_names.get(analyst_key, analyst) }}{% if not loop.last %}<br>{% endif %}
                                                        {% endfor %}
                                                        ">
                                                        分析师
                                                </button>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('view_record', record_id=record.id, from_page='history') }}" class="btn btn-sm btn-outline-primary">
                                                查看详情
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <!-- 添加分页控件 -->
                        <nav aria-label="交易记录分页">
                            <ul class="pagination justify-content-center" id="trading-pagination"></ul>
                        </nav>
                        {% else %}
                        <div class="alert alert-info">
                            暂无交易记录。请先进行交易操作。
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 回测记录 -->
            <div class="tab-pane fade" id="backtest-records" role="tabpanel" aria-labelledby="backtest-tab">
                <div class="card">
                    <div class="card-body">
                        {% set backtest_records = records|selectattr('type', 'equalto', 'backtest')|list %}
                        {% if backtest_records %}
                        
                        <!-- 添加股票筛选功能 -->
                        <div class="filter-container">
                            <div class="d-flex align-items-center">
                                <label for="backtest-stock-filter" class="form-label me-2 mb-0">按股票筛选:</label>
                                <select class="form-select form-select-sm me-2" id="backtest-stock-filter" style="width: auto; min-width: 150px;">
                                    <option value="all">全部股票</option>
                                    {% set all_tickers = [] %}
                                    {% for record in backtest_records %}
                                        {% for ticker in record.tickers %}
                                            {% if ticker not in all_tickers %}
                                                {% set _ = all_tickers.append(ticker) %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                    {% for ticker in all_tickers|sort %}
                                        <option value="{{ ticker }}">{{ ticker }}</option>
                                    {% endfor %}
                                </select>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="backtest-filter-reset">重置</button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="backtest-records-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>股票</th>
                                        <th>起始日期</th>
                                        <th>结束日期</th>
                                        <th>初始资金</th>
                                        <th>总收益率</th>
                                        <th>夏普比率</th>
                                        <th>模型</th>
                                        <th>分析师</th>
                                        <th>详情</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in backtest_records %}
                                    <tr data-tickers="{{ ','.join(record.tickers) }}">
                                        <td><span class="timestamp">{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</span></td>
                                        <td>{{ ', '.join(record.tickers) }}</td>
                                        <td>{{ record.start_date }}</td>
                                        <td>{{ record.end_date }}</td>
                                        <td>${{ record.initial_cash }}</td>
                                        <td class="{{ 'text-success' if record.metrics and record.metrics.get('total_return') and (record.metrics.get('total_return') > 0 if record.metrics.get('total_return')|float else False) else 'text-danger' }}">
                                            {% if record.metrics and record.metrics.get('total_return') is not none %}
                                                {% set total_return = record.metrics.get('total_return')|float %}
                                                {% if total_return <= 1 and total_return >= -1 %}
                                                    {{ "%.2f"|format(total_return * 100) }}%
                                                {% else %}
                                                    {{ "%.2f"|format(total_return) }}%
                                                {% endif %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.metrics and record.metrics.get('sharpe_ratio') is not none %}
                                                {{ "%.2f"|format(record.metrics.get('sharpe_ratio')) }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td><span class="model-name">{{ record.model_choice }}</span></td>
                                        <td>
                                            {% if record.selected_analysts %}
                                                <button type="button" 
                                                        class="btn btn-sm btn-reason" 
                                                        data-bs-toggle="popover" 
                                                        data-bs-trigger="click"
                                                        data-bs-html="true"
                                                        data-bs-content="
                                                        {% for analyst in record.selected_analysts %}
                                                            {% set analyst_key = analyst %}
                                                            {% if not analyst.endswith('_agent') %}
                                                                {% if analyst in ['fundamentals_analyst', 'sentiment_analyst', 'valuation_analyst'] %}
                                                                    {% set analyst_key = analyst.replace('_analyst', '_agent') %}
                                                                {% else %}
                                                                    {% set analyst_key = analyst + '_agent' %}
                                                                {% endif %}
                                                            {% endif %}
                                                            {{ analyst_names.get(analyst_key, analyst) }}{% if not loop.last %}<br>{% endif %}
                                                        {% endfor %}
                                                        ">
                                                        分析师
                                                </button>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('view_record', record_id=record.id, from_page='history') }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> 查看详情
                                            </a>
                                        </td>
                                        <td>
                                            {% if record.metrics and record.metrics.status %}
                                                {% if record.metrics.status == 'running' %}
                                                    <span class="badge bg-info">进行中</span>
                                                {% elif record.metrics.status == 'completed' %}
                                                    <span class="badge bg-success">已完成</span>
                                                {% elif record.metrics.status == 'failed' %}
                                                    <span class="badge bg-danger">失败</span>
                                                {% endif %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <!-- 添加分页控件 -->
                        <nav aria-label="回测记录分页">
                            <ul class="pagination justify-content-center" id="backtest-pagination"></ul>
                        </nav>
                        {% else %}
                        <div class="alert alert-info">
                            暂无回测记录。请先进行回测操作。
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/history.js') }}"></script>
</body>
</html>
