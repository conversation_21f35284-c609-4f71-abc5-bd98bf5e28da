<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .metric-positive {
            color: green;
            font-weight: bold;
        }
        .metric-negative {
            color: red;
            font-weight: bold;
        }
        .action-buy, .action-cover {
            color: rgb(40, 167, 69);
            font-weight: bold;
        }
        .action-sell {
            color: rgb(220, 53, 69);
            font-weight: bold;
        }
        .action-short {
            color: rgb(255, 193, 7);
            font-weight: bold;
        }
        .action-hold {
            color: rgb(108, 117, 125);
            font-weight: bold;
        }
        /* 添加理由按钮样式 */
        .btn-reason {
            color: #6c757d;
            background-color: transparent;
            border-color: #6c757d;
            opacity: 0.7;
            font-size: 0.9em;
            margin-right: 5px;
        }
        .btn-reason:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 1;
        }
        /* 股票价格图表样式 */
        .stock-chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .stock-selector-container {
            margin-bottom: 15px;
        }
        .stock-selector-container .btn {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .no-data-message {
            text-align: center;
            padding: 50px 0;
            color: #6c757d;
        }
        .task-status-active {
            color: #198754;
        }
        .task-status-paused {
            color: #fd7e14;
        }
        .task-status-completed {
            color: #0d6efd;
        }
        .task-status-error {
            color: #dc3545;
        }
        .execution-history {
            max-height: 600px;
            overflow-y: auto;
        }
        .table-responsive {
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            font-weight: normal;
        }
        .text-success {
            color: #198754;
            font-weight: bold;
        }
        .text-danger {
            color: #dc3545;
            font-weight: bold;
        }
        .text-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .text-info {
            color: #17a2b8;
            font-weight: bold;
        }
        .execution-price {
            font-family: monospace;
        }
        .signal-bullish {
            color: green;
            font-weight: bold;
        }
        .signal-bearish {
            color: red;
            font-weight: bold;
        }
        .signal-neutral {
            color: orange;
            font-weight: bold;
        }
        /* Chart.js Tooltip 样式 */
        #chartjs-tooltip {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.25);
            padding: 6px;
            pointer-events: none;
            position: absolute;
            transform: translate(-50%, -100%);
            z-index: 9999;
        }
        
        #chartjs-tooltip table {
            margin: 0;
        }
        
        #chartjs-tooltip .analyst-signals {
            margin-top: 4px;
            padding-top: 4px;
            border-top: 1px solid #eee;
        }
        
        #chartjs-tooltip .signal-bullish {
            color: #28a745;
            font-weight: bold;
        }
        
        #chartjs-tooltip .signal-bearish {
            color: #dc3545;
            font-weight: bold;
        }
        
        #chartjs-tooltip .signal-neutral {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>任务详情 - {{ task.name }}</h1>
            <a href="{{ url_for('task_queue') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回任务队列
            </a>
        </div>
        
        <!-- 任务信息卡片 -->
        <div class="card">
            <div class="card-header">
                <h3>任务信息</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>任务名称</th>
                                    <td>{{ task.name }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if task.status == 'active' %}
                                        <span class="task-status-active"><i class="bi bi-play-circle"></i> 活跃</span>
                                        {% elif task.status == 'paused' %}
                                        <span class="task-status-paused"><i class="bi bi-pause-circle"></i> 已暂停</span>
                                        {% elif task.status == 'completed' %}
                                        <span class="task-status-completed"><i class="bi bi-check-circle"></i> 已完成</span>
                                        {% elif task.status == 'error' %}
                                        <span class="task-status-error"><i class="bi bi-exclamation-circle"></i> 错误</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>股票</th>
                                    <td>{{ task.tickers }}</td>
                                </tr>
                                <tr>
                                    <th>执行间隔</th>
                                    <td>{{ task.execution_interval }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>下次执行时间</th>
                                    <td>{{ task.next_run }}</td>
                                </tr>
                                <tr>
                                    <th>结束条件</th>
                                    <td>
                                        {% if task.end_condition == 'count' %}
                                        执行 {{ task.end_count }} 次后结束
                                        {% elif task.end_condition == 'date' %}
                                        在 {{ task.end_date }} 结束
                                        {% else %}
                                        无结束条件
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>已执行次数</th>
                                    <td>{{ task.execution_count }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ task.created_at }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 d-flex justify-content-end">
                        {% if task.status == 'active' %}
                        <button class="btn btn-warning me-2" id="pauseTaskBtn" data-task-id="{{ task.id }}">
                            <i class="bi bi-pause-fill"></i> 暂停任务
                        </button>
                        {% elif task.status == 'paused' %}
                        <button class="btn btn-success me-2" id="resumeTaskBtn" data-task-id="{{ task.id }}">
                            <i class="bi bi-play-fill"></i> 恢复任务
                        </button>
                        {% endif %}
                        <button class="btn btn-danger" id="deleteTaskBtn" data-task-id="{{ task.id }}">
                            <i class="bi bi-trash"></i> 删除任务
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 股票价格图表卡片 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">股票价格走势</h3>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm active" data-filter="hour">最近4小时</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-filter="today">今日</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-filter="all">全部</button>
                </div>
            </div>
            <div class="card-body">
                <div class="stock-selector-container" id="stockSelector">
                    <!-- 股票选择按钮将在这里动态生成 -->
                </div>
                <div class="stock-chart-container">
                    <canvas id="stockPriceChart"></canvas>
                </div>
                <!-- 添加图例说明 -->
                <div class="mt-3">
                    <div class="d-flex justify-content-center gap-1">
                        <div><span class="badge bg-success">买入</span></div>
                        <div><span class="badge bg-danger">卖出</span></div>
                        <div><span class="badge bg-warning">做空</span></div>
                        <div><span class="badge bg-success">回补</span></div>
                        <div><span class="badge bg-secondary">持有</span></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 任务执行历史卡片 -->
        <div class="card">
            <div class="card-header">
                <h3>执行历史</h3>
            </div>
            <div class="card-body execution-history">
                {% if executions|length > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>执行时间</th>
                                <th>操作</th>
                                <th>数量</th>
                                <th>信心度</th>
                                <th>执行价格</th>
                                <th>决策理由</th>
                                <th>分析师信号</th>
                                <th>状态</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for execution in executions %}
                            <tr>
                                <td><span class="timestamp">{{ execution.execution_time }}</span></td>
                                <td>
                                    {% if execution.decisions %}
                                        {% for ticker, decision in execution.decisions.items() %}
                                            {% if decision.action == "buy" %}
                                            <span class="action-buy">买入</span>
                                            {% elif decision.action == "sell" %}
                                            <span class="action-sell">卖出</span>
                                            {% elif decision.action == "short" %}
                                            <span class="action-short">做空</span>
                                            {% elif decision.action == "cover" %}
                                            <span class="action-cover">回补</span>
                                            {% else %}
                                            <span class="action-hold">持有</span>
                                            {% endif %}
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.decisions %}
                                        {% for ticker, decision in execution.decisions.items() %}
                                            {{ decision.quantity }}
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.decisions %}
                                        {% for ticker, decision in execution.decisions.items() %}
                                            {{ decision.confidence }}%
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.decisions %}
                                        {% for ticker, decision in execution.decisions.items() %}
                                            <span class="execution-price" data-ticker="{{ ticker }}" data-time="{{ execution.execution_time }}">-</span>
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.decisions %}
                                        {% for ticker, decision in execution.decisions.items() %}
                                            {% if decision.reasoning %}
                                                <button type="button" 
                                                    class="btn btn-sm btn-reason" 
                                                    data-bs-toggle="popover" 
                                                    data-bs-trigger="click"
                                                    data-bs-html="true"
                                                    data-bs-content="{{ decision.reasoning }}">
                                                    {{ ticker }}
                                                </button>
                                            {% endif %}
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.analyst_signals %}
                                        {% for ticker in execution.decisions.keys() %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-reason" 
                                                    data-bs-toggle="popover" 
                                                    data-bs-trigger="click"
                                                    data-bs-html="true"
                                                    data-bs-content="
                                                    {% for analyst, signals in execution.analyst_signals.items() %}
                                                        {% if ticker in signals %}
                                                        <strong>{{ analyst_names.get(analyst, analyst) }}</strong>: 
                                                        {% if signals[ticker].signal == 'bullish' %}
                                                        <span class='signal-bullish'>看涨</span>
                                                        {% elif signals[ticker].signal == 'bearish' %}
                                                        <span class='signal-bearish'>看跌</span>
                                                        {% else %}
                                                        <span class='signal-neutral'>中性</span>
                                                        {% endif %}
                                                        {% if signals[ticker].confidence is defined %}({{ signals[ticker].confidence }}%){% endif %}
                                                        <br>
                                                        {% endif %}
                                                    {% endfor %}
                                                    ">
                                                    {{ ticker }}
                                            </button>
                                            {% if not loop.last %}, {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.status == 'success' %}
                                    <span class="badge bg-success">成功</span>
                                    {% elif execution.status == 'failed' %}
                                    <span class="badge bg-danger">失败</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.record_id %}
                                    <a href="{{ url_for('view_record', record_id=execution.record_id, from_page='task_detail', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                        查看详情
                                    </a>
                                    {% else %}
                                    <span class="text-muted">无结果</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4 no-data-message">
                    <i class="bi bi-calendar-x fs-1 text-muted"></i>
                    <p class="mt-3 text-muted">暂无执行历史</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
    <!-- 添加数据变量 -->
    <script>
        // 将模板变量传递给JavaScript
        const stockPricesDataStr = '{{ stock_prices|safe }}';
        const executionsJsonData = '{{ executions_json|safe }}';
        const analystNamesData = JSON.parse('{{ analyst_names|tojson|safe }}');
    </script>
    <!-- 引用外部JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/task_detail.js') }}"></script>
</body>
</html>
