<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务已添加</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4>任务已成功添加到队列</h4>
            </div>
            <div class="card-body">
                <h5 class="card-title">{{ task_name }}</h5>
                <p class="card-text">任务ID: {{ task_id }}</p>
                
                {% if executed_immediately %}
                <div class="alert alert-info">
                    <p>已执行一次任务。</p>
                    {% if record_id %}
                    <p>执行结果: <a href="{{ url_for('view_record', record_id=record_id, from_page='task_queue') }}">查看详情</a></p>
                    {% endif %}
                </div>
                {% endif %}
                
                <div class="mt-4">
                    <a href="{{ url_for('task_queue') }}" class="btn btn-primary">查看任务队列</a>
                    <a href="{{ url_for('index') }}" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>