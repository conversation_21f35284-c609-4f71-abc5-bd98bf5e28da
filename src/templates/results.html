<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易结果</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .execution-log {
            background-color: #000;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .card {
            margin-bottom: 20px;
        }
        .signal-bullish {
            color: green;
            font-weight: bold;
        }
        .signal-bearish {
            color: red;
            font-weight: bold;
        }
        .signal-neutral {
            color: orange;
            font-weight: bold;
        }
        .action-buy, .action-cover {
            color: green;
            font-weight: bold;
        }
        .action-sell, .action-short {
            color: red;
            font-weight: bold;
        }
        .action-hold {
            color: gray;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>交易结果 #{{ record_id }}</h1>
            {% if request.args.get('from_page') == 'task_detail' %}
            <a href="{{ url_for('task_detail', task_id=request.args.get('task_id')) }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回任务详情
            </a>
            {% elif request.args.get('from_page') == 'task_queue' %}
            <a href="{{ url_for('task_queue') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回任务队列
            </a>
            {% elif request.args.get('from_page') == 'history' %}
            <a href="{{ url_for('history') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回历史记录
            </a>
            {% else %}
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回首页
            </a>
            {% endif %}
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>交易决策</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>股票</th>
                            <th>操作</th>
                            <th>数量</th>
                            <th>信心度</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ticker, decision in result.decisions.items() %}
                        <tr>
                            <td>{{ ticker }}</td>
                            <td>
                                {% if decision.action == "buy" %}
                                <span class="action-buy">买入</span>
                                {% elif decision.action == "sell" %}
                                <span class="action-sell">卖出</span>
                                {% elif decision.action == "short" %}
                                <span class="action-short">做空</span>
                                {% elif decision.action == "cover" %}
                                <span class="action-cover">回补</span>
                                {% else %}
                                <span class="action-hold">持有</span>
                                {% endif %}
                            </td>
                            <td>{{ decision.quantity }}</td>
                            <td>{{ decision.confidence }}%</td>
                        </tr>
                        {% if decision.reasoning %}
                        <tr>
                            <td colspan="4" class="text-muted" style="background-color: #f8f9fa; font-size: 0.9em; padding: 10px;">
                                <strong>决策理由：</strong>{{ decision.reasoning }}
                            </td>
                        </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 添加分析师信号部分 -->
        <div class="card">
            <div class="card-header">
                <h5>分析师信号</h5>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group mb-2" role="group" aria-label="股票选择">
                        {% for ticker in tickers %}
                        <button type="button" class="btn btn-outline-primary stock-selector" data-ticker="{{ ticker }}">{{ ticker }}</button>
                        {% endfor %}
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#signalsCollapse" aria-expanded="true">
                        显示/隐藏分析结果
                    </button>
                </div>
            </div>
            <div class="collapse show" id="signalsCollapse">
                <div class="card-body">
                    {% for ticker in tickers %}
                    <div class="stock-data" id="stock-{{ ticker }}" {% if not loop.first %}style="display: none;"{% endif %}>
                        <h6 class="mb-3">
                            {{ ticker }} 分析结果
                            {% if 'risk_management_agent' in result.analyst_signals and ticker in result.analyst_signals.risk_management_agent and result.analyst_signals.risk_management_agent[ticker].current_price %}
                            <small class="text-muted">(价格: ${{ result.analyst_signals.risk_management_agent[ticker].current_price }})</small>
                            {% endif %}
                        </h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th style="min-width: 140px;">分析师</th>
                                    <th style="min-width: 80px;">信号</th>
                                    <th style="min-width: 80px;">信心度</th>
                                    <th>分析原因</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analyst, signals in result.analyst_signals.items() %}
                                    {% if ticker in signals %}
                                    <tr>
                                        <td>{{ analyst_names.get(analyst, analyst) }}</td>
                                        <td>
                                            {% if signals[ticker].signal == "bullish" %}
                                            <span class="signal-bullish">看涨</span>
                                            {% elif signals[ticker].signal == "bearish" %}
                                            <span class="signal-bearish">看跌</span>
                                            {% else %}
                                            <span class="signal-neutral">中性</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ signals[ticker].confidence }}%</td>
                                        <td>
                                            {% if signals[ticker].reasoning %}
                                                {{ signals[ticker].reasoning }}
                                            {% elif signals[ticker].reason %}
                                                {{ signals[ticker].reason }}
                                            {% elif signals[ticker].rationale %}
                                                {{ signals[ticker].rationale }}
                                            {% else %}
                                                <small class="text-muted">未提供分析原因</small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>执行日志</h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#logCollapse" aria-expanded="false">
                    显示/隐藏日志
                </button>
            </div>
            <div class="collapse" id="logCollapse">
                <div class="card-body">
                    <div class="execution-log">{{ execution_log }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 股票切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const stockButtons = document.querySelectorAll('.stock-selector');
            const stockData = document.querySelectorAll('.stock-data');
            
            // 设置第一个按钮为激活状态
            if(stockButtons.length > 0) {
                stockButtons[0].classList.add('active');
            }
            
            stockButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    stockButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前按钮的激活状态
                    this.classList.add('active');
                    
                    // 隐藏所有股票数据
                    stockData.forEach(data => data.style.display = 'none');
                    
                    // 显示选中的股票数据
                    const selectedTicker = this.getAttribute('data-ticker');
                    const selectedData = document.getElementById('stock-' + selectedTicker);
                    if(selectedData) {
                        selectedData.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html>
