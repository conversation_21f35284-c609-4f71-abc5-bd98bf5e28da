<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Fund</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: 0;
            background-color: white;
        }
        .analyst-checkbox {
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .card {
            margin-bottom: 20px;
        }
        
        /* 导航栏样式 */
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
            background-color: white !important;
            padding: 0.8rem 1rem;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.4rem;
        }
        
        .nav-link {
            color: #6c757d !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #2c3e50 !important;
            background-color: #f8f9fa;
        }
        
        .nav-link.active {
            color: #007bff !important;
            background-color: rgba(0,123,255,.1);
        }
        
        .user-info .btn-outline-secondary {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }
        
        .user-info .btn-outline-secondary:hover,
        .user-info .btn-outline-secondary:focus {
            color: #2c3e50;
            background-color: #f8f9fa;
            box-shadow: none;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            border-radius: 0.5rem;
        }
        
        .dropdown-item {
            padding: 0.5rem 1.5rem;
            color: #6c757d;
        }
        
        .dropdown-item:hover {
            color: #2c3e50;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light mb-4 rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="bi bi-graph-up"></i> AI Fund
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="bi bi-house"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'history' %}active{% endif %}" href="{{ url_for('history') }}">
                                <i class="bi bi-clock-history"></i> 历史记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'task_queue' %}active{% endif %}" href="{{ url_for('task_queue') }}">
                                <i class="bi bi-list-task"></i> 任务队列
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'data_monitor' %}active{% endif %}" href="{{ url_for('data_monitor') }}">
                                <i class="bi bi-database-gear"></i> 数据监控
                            </a>
                        </li>
                    </ul>
                    <div class="user-info">
                        {% if user and not config.get('dev_mode', False) %}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i> {{ user.username if user.username else session.get('username', 'User') }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> 退出登录
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- 活跃任务提示 -->
        {% if active_tasks > 0 %}
        <div class="alert alert-info mb-4" role="alert">
            当前有 <strong>{{ active_tasks }}</strong> 个活跃任务正在运行
        </div>
        {% endif %}
        
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="trading-tab" data-bs-toggle="tab" data-bs-target="#trading" type="button" role="tab" aria-controls="trading" aria-selected="true">交易</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="backtest-tab" data-bs-toggle="tab" data-bs-target="#backtest" type="button" role="tab" aria-controls="backtest" aria-selected="false">回测</button>
            </li>
        </ul>
        
        <div class="tab-content" id="myTabContent">
            <!-- 交易表单 -->
            <div class="tab-pane fade show active" id="trading" role="tabpanel" aria-labelledby="trading-tab">
                <form action="/run_trading" method="post">
                    <div class="card">
                        <div class="card-header">
                            <h5>基本设置</h5>
                        </div>
                        <div class="card-body">
                            <!-- 交易表单中的股票选择 -->
                            <div class="mb-3">
                                <label for="tickers" class="form-label">股票代码（多选）</label>
                                <select class="form-select" id="tickers" name="tickers[]" multiple data-placeholder="选择或输入股票代码" required>
                                    {% for stock in stocks %}
                                    <option value="{{ stock.value }}">{{ stock.display }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">可以选择多个股票代码，也可以直接输入</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="trading_date" class="form-label">交易日期</label>
                                        <input type="datetime-local" class="form-control" id="trading_date" name="trading_date">
                                        <small class="text-muted">默认为当前时间</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="initial_cash" class="form-label">初始现金</label>
                                        <input type="number" class="form-control" id="initial_cash" name="initial_cash" value="100000" min="1000">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="margin_requirement" class="form-label">保证金要求</label>
                                        <input type="number" class="form-control" id="margin_requirement" name="margin_requirement" value="0" min="0" max="1" step="0.01">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 交易表单中的推理过程复选框 -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="show_reasoning" name="show_reasoning" checked>
                                <label class="form-check-label" for="show_reasoning">显示推理过程</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>选择分析师</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-outline-primary mb-3" id="select-all-analysts">全选</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary mb-3" id="deselect-all-analysts">取消全选</button>
                                
                                <div class="analyst-checkboxes">
                                    {% for analyst in analysts %}
                                    <div class="form-check form-check-inline analyst-checkbox">
                                        <input class="form-check-input" type="checkbox" name="analysts" id="analyst-{{ analyst.value }}" value="{{ analyst.value }}" 
                                               {% if analyst.value in ['technical_analyst', 'fundamentals_analyst', 'sentiment_analyst'] %}checked{% endif %}>
                                        <label class="form-check-label" for="analyst-{{ analyst.value }}">{{ analyst.display }}</label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>选择模型</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <select class="form-select" name="model" id="model" required>
                                    <option value="" disabled>请选择LLM模型</option>
                                    {% for model in models %}
                                    <option value="{{ model.value }}" {% if model.value == default_model %}selected{% endif %}>{{ model.display }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 交易表单中添加定时执行选项 -->
                    <div class="card">
                        <div class="card-header">
                            <h5>定时执行</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="enable_schedule" name="enable_schedule">
                                <label class="form-check-label" for="enable_schedule">启用定时执行</label>
                            </div>
                            
                            <div id="schedule-options" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="execution_interval" class="form-label">执行频率</label>
                                            <select class="form-select" id="execution_interval" name="execution_interval">
                                                <option value="1m">每分钟</option>
                                                <option value="3m">每3分钟</option>
                                                <option value="5m" selected>每5分钟</option>
                                                <option value="15m">每15分钟</option>
                                                <option value="30m">每30分钟</option>
                                                <option value="1h">每1小时</option>
                                                <option value="4h">每4小时</option>
                                                <option value="1d">每天</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_after" class="form-label">结束条件</label>
                                            <div class="input-group">
                                                <select class="form-select" id="end_condition" name="end_condition">
                                                    <option value="never">永不结束</option>
                                                    <option value="count">执行次数</option>
                                                    <option value="date">截止日期</option>
                                                </select>
                                                <input type="number" class="form-control" id="end_count" name="end_count" value="10" min="1" style="display: none;">
                                                <input type="date" class="form-control" id="end_date" name="end_date" style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 执行时段设置 -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="execution_time_start" class="form-label">执行时段（开始）</label>
                                            <input type="time" class="form-control" id="execution_time_start" name="execution_time_start" value="21:30">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="execution_time_end" class="form-label">执行时段（结束）</label>
                                            <input type="time" class="form-control" id="execution_time_end" name="execution_time_end" value="04:30">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="execute_immediately" name="execute_immediately" checked>
                                    <label class="form-check-label" for="execute_immediately">立即执行</label>
                                </div>
                                <div class="mb-3">
                                    <label for="task_name" class="form-label">任务名称</label>
                                    <input type="text" class="form-control" id="task_name" name="task_name" placeholder="可选，用于在队列中识别此任务">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <div class="btn-group">
                            <button type="submit" class="btn btn-primary btn-lg" id="trading-submit-btn" name="action" value="run_now">立即运行</button>
                            <button type="submit" class="btn btn-outline-primary btn-lg" id="trading-queue-btn" name="action" value="add_to_queue" style="display: none;">加入队列</button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 回测表单 -->
            <div class="tab-pane fade" id="backtest" role="tabpanel" aria-labelledby="backtest-tab">
                <form action="/run_backtest" method="post">
                    <div class="card">
                        <div class="card-header">
                            <h5>基本设置</h5>
                        </div>
                        <div class="card-body">
                            <!-- 回测表单中的股票选择 -->
                            <div class="mb-3">
                                <label for="backtest-tickers" class="form-label">股票代码（多选）</label>
                                <select class="form-select" id="backtest-tickers" name="tickers[]" multiple data-placeholder="选择或输入股票代码" required>
                                    {% for stock in stocks %}
                                    <option value="{{ stock.value }}">{{ stock.display }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">可以选择多个股票代码，也可以直接输入</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="backtest-use-default-period" name="use_default_period">
                                        <label class="form-check-label" for="backtest-use-default-period">
                                            <span class="badge bg-info me-1">测试</span> 使用默认时间段 (2025.4.10 - 2025.5.10)
                                        </label>
                                        <small class="form-text text-muted d-block">此选项使用预设的测试数据时间段，适合快速验证系统功能</small>
                                        <input type="hidden" name="default_start_date" value="2025-04-10">
                                        <input type="hidden" name="default_end_date" value="2025-05-10">
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="backtest-date-inputs">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backtest-start_date" class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="backtest-start_date" name="start_date">
                                        <small class="text-muted">默认为结束日期前1个月</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backtest-end_date" class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="backtest-end_date" name="end_date">
                                        <small class="text-muted">默认为今天</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backtest-initial_cash" class="form-label">初始资本</label>
                                        <input type="number" class="form-control" id="backtest-initial_cash" name="initial_cash" value="100000" min="1000">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backtest-margin_requirement" class="form-label">保证金要求</label>
                                        <input type="number" class="form-control" id="backtest-margin_requirement" name="margin_requirement" value="0" min="0" max="1" step="0.01">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="backtest-show_reasoning" name="show_reasoning" checked>
                                <label class="form-check-label" for="backtest-show_reasoning">显示推理过程</label>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="backtest-force_refresh" name="force_refresh">
                                <label class="form-check-label" for="backtest-force_refresh">强制刷新数据</label>
                                <small class="form-text text-muted d-block">启用后将忽略缓存，重新获取所有数据</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>选择分析师</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-outline-primary mb-3" id="backtest-select-all-analysts">全选</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary mb-3" id="backtest-deselect-all-analysts">取消全选</button>
                                
                                <div class="analyst-checkboxes">
                                    {% for analyst in analysts %}
                                    <div class="form-check form-check-inline analyst-checkbox">
                                        <input class="form-check-input" type="checkbox" name="analysts" id="backtest-analyst-{{ analyst.value }}" value="{{ analyst.value }}"
                                               {% if analyst.value in ['technical_analyst', 'fundamentals_analyst', 'sentiment_analyst'] %}checked{% endif %}>
                                        <label class="form-check-label" for="backtest-analyst-{{ analyst.value }}">{{ analyst.display }}</label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>选择模型</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <select class="form-select" name="model" id="backtest-model" required>
                                    <option value="" disabled>请选择LLM模型</option>
                                    {% for model in models %}
                                    <option value="{{ model.value }}" {% if model.value == default_model %}selected{% endif %}>{{ model.display }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg" id="backtest-submit-btn">运行回测</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 定义默认股票变量
        var defaultStock = "{{ default_stock }}";
    </script>
    <script src="{{ url_for('static', filename='js/index.js') }}"></script>
</body>
</html>
