<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测进行中</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .spinner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
        }
        .spinner-border {
            width: 5rem;
            height: 5rem;
            margin-bottom: 20px;
        }
    </style>
    <!-- <meta http-equiv="refresh" content="10"> -->
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>回测进行中</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回首页
            </a>
        </div>
        
        <div class="card">
            <div class="card-header bg-info text-white">
                <h4><i class="bi bi-hourglass-split me-2"></i>回测正在进行中</h4>
            </div>
            <div class="card-body">
                <div class="spinner-container">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h4>正在执行回测，请稍候...</h4>
                </div>
                
                <div class="alert alert-info mt-4">
                    <p><i class="bi bi-info-circle me-2"></i>回测任务正在后台执行中。根据回测的时间范围和复杂度，可能需要几分钟到几十分钟不等。</p>
                    <p>此页面将每10秒自动刷新一次，检查回测是否完成。</p>
                </div>
                
                <p>回测任务ID: <strong>{{ record_id }}</strong></p>
                
                <div class="mt-4">
                    <a href="{{ url_for('history') }}" class="btn btn-primary">
                        <i class="bi bi-clock-history me-2"></i>查看历史记录
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-secondary">
                        <i class="bi bi-house me-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function checkBacktestStatus() {
            $.ajax({
                url: '/check_backtest_status/{{ record_id }}',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.completed) {
                        // 回测完成，重定向到结果页面
                        window.location.href = response.redirect_url;
                    }
                },
                error: function(xhr, status, error) {
                    console.error('检查回测状态时出错:', error);
                }
            });
        }

        // 每5秒检查一次状态
        $(document).ready(function() {
            setInterval(checkBacktestStatus, 5000);
        });
    </script>
</body>
</html>