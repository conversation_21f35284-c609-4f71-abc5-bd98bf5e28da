<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测结果</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
        .metric-positive {
            color: green;
            font-weight: bold;
        }
        .metric-negative {
            color: red;
            font-weight: bold;
        }
        .execution-log {
            background-color: #000;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        /* 添加提示样式 */
        .metric-info {
            cursor: help;
            margin-left: 5px;
        }
        .tooltip-inner {
            max-width: 300px;
            text-align: left;
        }
        /* 股票价格图表样式 */
        .stock-chart-container {
            position: relative;
            height: 400px;
        }
        .stock-selector-container {
            margin-bottom: 15px;
        }
        .stock-selector-container .btn {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .no-data-message {
            text-align: center;
            padding: 50px 0;
            color: #6c757d;
        }
        /* 决策和分析师信号样式 */
        .action-buy, .signal-bullish {
            color: green;
            font-weight: bold;
        }
        .action-sell, .action-short, .signal-bearish {
            color: red;
            font-weight: bold;
        }
        .action-cover {
            color: green;
            font-weight: bold;
        }
        .action-hold, .signal-neutral {
            color: #6c757d;
            font-weight: bold;
        }
        .decision-reasoning {
            font-style: italic;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin-top: 5px;
            font-size: 0.9em;
        }
        /* 分析原因单元格样式 */
        .table td:last-child {
            max-width: 300px;
            white-space: normal;
            word-break: break-word;
        }
        
        /* 分析原因展开/收起样式 */
        .reasoning-truncated {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
        }
        
        .reasoning-content {
            width: calc(100% - 70px); /* 减去按钮宽度和间距 */
        }
        
        .reasoning-toggle {
            color: #0d6efd;
            cursor: pointer;
            font-size: 0.85em;
            white-space: nowrap;
            align-self: flex-start;
        }
        
        .reasoning-toggle:hover {
            text-decoration: underline;
        }
        
        .reasoning-container {
            position: relative;
        }
        .date-selector-container {
            max-width: 300px;
        }
        /* 决策理由部分样式 */
        .decision-reasoning-section {
            border-left: 3px solid #28a745;
            padding-left: 10px;
        }
        
        .decision-reasoning-section h6 {
            color: #28a745;
            font-weight: 600;
        }
        
        .decision-reasoning-full {
            line-height: 1.5;
            font-size: 0.95em;
        }
        /* 添加投资组合图表样式 */
        #portfolioChart {
            height: 400px !important;
            width: 100% !important;
        }
        
        .portfolio-chart-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .portfolio-summary {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
            z-index: 10;
        }
        
        .portfolio-summary .total-return {
            font-weight: bold;
        }
        
        .portfolio-summary .positive {
            color: #28a745;
        }
        
        .portfolio-summary .negative {
            color: #dc3545;
        }
        /* 确保性能指标表格在小屏幕上也能正常显示 */
        @media (max-width: 768px) {
            .col-md-9, .col-md-3 {
                width: 100%;
            }
            
            .col-md-3 {
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>回测结果 #{{ record_id }}</h1>
            {% if request.args.get('from_page') == 'task_detail' %}
            <a href="{{ url_for('task_detail', task_id=request.args.get('task_id')) }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回任务详情
            </a>
            {% elif request.args.get('from_page') == 'task_queue' %}
            <a href="{{ url_for('task_queue') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回任务队列
            </a>
            {% elif request.args.get('from_page') == 'history' %}
            <a href="{{ url_for('history') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回历史记录
            </a>
            {% else %}
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回首页
            </a>
            {% endif %}
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>投资组合表现</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-9">
                        <canvas id="portfolioChart"></canvas>
                    </div>
                    <div class="col-md-3">
                        <h4>性能指标</h4>
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>
                                        总收益率
                                        <i class="bi bi-info-circle metric-info" 
                                           data-bs-toggle="tooltip" 
                                           data-bs-placement="right"
                                           title="总收益率表示投资组合从开始到结束的总体收益百分比。正值表示盈利，负值表示亏损。"></i>
                                    </th>
                                    <td class="{{ 'metric-positive' if metrics.get('total_return') is not none and metrics.get('total_return', 0) > 0 else 'metric-negative' }}">
                                        {{ "%.2f%%"|format(metrics.get('total_return', 0)) if metrics.get('total_return') is not none else 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        夏普比率
                                        <i class="bi bi-info-circle metric-info" 
                                           data-bs-toggle="tooltip" 
                                           data-bs-placement="right"
                                           title="夏普比率衡量投资组合的超额回报与风险的比率。数值越高表示每单位风险获得的回报越高。通常大于1被认为是良好的，大于2被认为是非常好的。"></i>
                                    </th>
                                    <td class="{{ 'metric-positive' if metrics.get('sharpe_ratio') is not none and metrics.get('sharpe_ratio', 0) > 0 else 'metric-negative' }}">
                                        {{ "%.2f"|format(metrics.get('sharpe_ratio', 0)) if metrics.get('sharpe_ratio') is not none else 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        索提诺比率
                                        <i class="bi bi-info-circle metric-info" 
                                           data-bs-toggle="tooltip" 
                                           data-bs-placement="right"
                                           title="索提诺比率类似于夏普比率，但只考虑下行风险（负回报）。它衡量投资组合的超额回报与下行风险的比率。数值越高表示每单位下行风险获得的回报越高，对于厌恶风险的投资者更有意义。"></i>
                                    </th>
                                    <td class="{{ 'metric-positive' if metrics.get('sortino_ratio') is not none and metrics.get('sortino_ratio', 0) > 0 else 'metric-negative' }}">
                                        {{ "%.2f"|format(metrics.get('sortino_ratio', 0)) if metrics.get('sortino_ratio') is not none else 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        最大回撤
                                        <i class="bi bi-info-circle metric-info" 
                                           data-bs-toggle="tooltip" 
                                           data-bs-placement="right"
                                           title="最大回撤是指投资组合从峰值到谷值的最大损失百分比。它衡量投资策略的下行风险，数值越小越好。"></i>
                                    </th>
                                    <td class="{{ 'metric-negative' if metrics.get('max_drawdown') is not none and metrics.get('max_drawdown', 0) < 0 else 'metric-positive' }}">
                                        {{ "%.2f%%"|format(metrics.get('max_drawdown', 0)) if metrics.get('max_drawdown') is not none else 'N/A' }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加股票价格图表卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">股票价格走势</h3>
            </div>
            <div class="card-body">
                <div class="stock-selector-container" id="stockSelector">
                    <!-- 股票选择按钮将在这里动态生成 -->
                </div>
                <div class="stock-chart-container">
                    <canvas id="stockPriceChart"></canvas>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-center">
                        <span class="badge bg-success me-2">买入</span>
                        <span class="badge bg-danger me-2">卖出</span>
                        <span class="badge bg-warning me-2">做空</span>
                        <span class="badge bg-success me-2">回补</span>
                        <span class="badge bg-secondary me-2">持有</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 决策和分析师信号卡片 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">交易决策与分析师信号</h3>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#decisionAnalystCollapse" aria-expanded="true">
                    <i class="bi bi-chevron-up"></i>
                </button>
            </div>
            <div class="collapse show" id="decisionAnalystCollapse">
                <div class="card-body">
                    <div id="decisionAnalystContainer">
                        <!-- 决策和分析师信号表格将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易历史和每日摘要卡片 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">回测详情</h3>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#tradeHistoryCollapse" aria-expanded="true">
                        <i class="bi bi-clock-history"></i> 交易历史
                    </button>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#dailySummaryCollapse">
                        <i class="bi bi-calendar3"></i> 每日摘要
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 交易历史表格 -->
                <div class="collapse show" id="tradeHistoryCollapse">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0"><i class="bi bi-clock-history"></i> 交易历史</h4>
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#tradeHistoryCollapse">
                            <i class="bi bi-chevron-up"></i>
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>日期</th>
                                    <th>股票</th>
                                    <th>操作</th>
                                    <th>数量</th>
                                    <th>价格</th>
                                    <th>持仓数量</th>
                                    <th>持仓价值</th>
                                    <th>看涨</th>
                                    <th>看跌</th>
                                    <th>中性</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if trade_history %}
                                    {% for trade in trade_history %}
                                    <tr>
                                        <td>{{ trade.date }}</td>
                                        <td>{{ trade.ticker }}</td>
                                        <td>
                                            {% if trade.action == 'BUY' or trade.action == 'buy' %}
                                            <span class="badge bg-success">买入</span>
                                            {% elif trade.action == 'SELL' or trade.action == 'sell' %}
                                            <span class="badge bg-danger">卖出</span>
                                            {% elif trade.action == 'SHORT' or trade.action == 'short' %}
                                            <span class="badge bg-warning">做空</span>
                                            {% elif trade.action == 'COVER' or trade.action == 'cover' %}
                                            <span class="badge bg-success">回补</span>
                                            {% else %}
                                            <span class="badge bg-secondary">持有</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ trade.quantity }}</td>
                                        <td>${{ "%.2f"|format(trade.price|float) }}</td>
                                        <td>{{ trade.shares_owned }}</td>
                                        <td>${{ "%.2f"|format(trade.position_value|float) }}</td>
                                        <td>{{ trade.bullish_count }}</td>
                                        <td>{{ trade.bearish_count }}</td>
                                        <td>{{ trade.neutral_count }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="10" class="text-center">无交易记录</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 每日摘要表格 -->
                <div class="collapse" id="dailySummaryCollapse">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0"><i class="bi bi-calendar3"></i> 每日摘要</h4>
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#dailySummaryCollapse">
                            <i class="bi bi-chevron-up"></i>
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>日期</th>
                                    <th>总价值</th>
                                    <th>收益率</th>
                                    <th>现金余额</th>
                                    <th>持仓价值</th>
                                    <th>夏普比率</th>
                                    <th>索提诺比率</th>
                                    <th>最大回撤</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if daily_summaries %}
                                    {% for summary in daily_summaries %}
                                    <tr>
                                        <td>{{ summary.date }}</td>
                                        <td>${{ "%.2f"|format(summary.total_value|float) }}</td>
                                        <td class="{{ 'text-success' if summary.return_pct|float > 0 else 'text-danger' }}">
                                            {{ "%.2f"|format(summary.return_pct|float) }}% {{ "↑" if summary.return_pct|float > 0 else "↓" }}
                                        </td>
                                        <td>${{ "%.2f"|format(summary.cash_balance|float) }}</td>
                                        <td>${{ "%.2f"|format(summary.total_position_value|float) }}</td>
                                        <td>{{ "%.2f"|format(summary.sharpe_ratio|float) if summary.sharpe_ratio else 'N/A' }}</td>
                                        <td>{{ "%.2f"|format(summary.sortino_ratio|float) if summary.sortino_ratio else 'N/A' }}</td>
                                        <td class="text-danger">{{ "%.2f"|format(summary.max_drawdown|float|abs) }}%</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">无每日摘要数据</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行日志卡片 -->
        <div class="card">
            <div class="card-header">
                <h3>执行日志</h3>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#executionLogCollapse" aria-expanded="false" aria-controls="executionLogCollapse">
                    显示/隐藏日志
                </button>
            </div>
            <div class="collapse" id="executionLogCollapse">
                <div class="card-body">
                    <div class="execution-log">{{ execution_log }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确保在页面底部正确加载所有必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>
    
    <script>
        // 传递数据到前端
        const portfolioValuesData = '{{ portfolio_values|safe }}';
        const stockPricesData = '{{ stock_prices|safe }}';
        const tradeHistoryData = '{{ trade_history|tojson }}';
        const dailySummariesData = '{{ daily_summaries|tojson }}';
        const metricsData = '{{ metrics|tojson }}';
        const dailyDecisionsData = '{{ daily_decisions|safe }}';
        const dailyAnalystSignalsData = '{{ daily_analyst_signals|safe }}';
        
        // 分析师名称映射
        const analystNamesData = '{{ analyst_names|tojson }}';
    </script>
    
    <!-- 确保在数据变量定义后加载图表脚本 -->
    <script src="{{ url_for('static', filename='js/backtest_chart.js') }}"></script>
</body>
</html>
