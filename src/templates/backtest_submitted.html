<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测已提交</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="bi bi-check-circle-fill me-2"></i>回测任务已提交</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p><i class="bi bi-info-circle me-2"></i>您的回测任务已成功提交到后台执行。回测可能需要几分钟到几十分钟不等，具体取决于回测的时间范围和复杂度。</p>
                </div>
                
                <p>回测任务ID: <strong>{{ record_id }}</strong></p>
                
                <p>您可以：</p>
                <ul>
                    <li>稍后在<a href="{{ url_for('history') }}">历史记录</a>页面查看回测结果</li>
                    <li>或者直接查看<a href="{{ url_for('view_record', record_id=record_id) }}">此回测任务</a>的状态</li>
                </ul>
                
                <div class="mt-4">
                    <a href="{{ url_for('history') }}" class="btn btn-primary">
                        <i class="bi bi-clock-history me-2"></i>查看历史记录
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-secondary">
                        <i class="bi bi-house me-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>