import json
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
from agents.portfolio_manager import portfolio_management_agent
from agents.risk_manager import risk_management_agent
from colorama import init
from utils.logger import get_logger
from utils.progress import progress
from graph.state import AgentState
from utils.analysts import get_analyst_nodes
from tools.api import get_prices

# Load environment variables from .env file
load_dotenv()
init(autoreset=True)

def parse_hedge_fund_response(response):
    """解析JSON字符串并返回字典。"""
    try:
        return json.loads(response)
    except json.JSONDecodeError as e:
        print(f"JSON解码错误: {e}\n响应: {repr(response)}")
        return None
    except TypeError as e:
        print(f"无效的响应类型（预期字符串，得到{type(response).__name__}）: {e}")
        return None
    except Exception as e:
        print(f"解析响应时发生意外错误: {e}\n响应: {repr(response)}")
        return None

def run_hedge_fund(
    tickers: list[str],
    start_date: str,
    end_date: str,
    portfolio: dict,
    show_reasoning: bool = True,
    selected_analysts: list[str] = [],
    model_name: str = "gpt-4o",
    model_provider: str = "OpenAI",
    interval: str = "minute",  # 时间间隔，{'second', 'minute', 'day', 'week', 'month', 'year'}
    interval_multiplier: int = 1,
):
    """
    Execute trading decisions based on analyst signals and market data.
    
    Args:
        tickers: List of stock tickers to analyze
        start_date: Start date for analysis
        end_date: End date for analysis
        portfolio: Current portfolio state
        show_reasoning: Whether to show reasoning process
        selected_analysts: List of analysts to use
        model_name: LLM model name
        model_provider: LLM provider
        interval: Time interval for data
        interval_multiplier: Multiplier for interval
        
    Returns:
        Dictionary containing trading decisions, analyst signals, and stock prices
    """
    # 开始进度跟踪
    progress.start()
    
    # 添加日志记录
    logger = get_logger('hedge_fund')
    logger.info(f"运行对冲基金，股票: {tickers}, 日期: {start_date} 到 {end_date}")

    try:
        # 创建工作流并编译代理
        workflow = create_workflow(selected_analysts)
        agent = workflow.compile()

        # 获取股票价格数据用于记录
        stock_prices_for_record = {}
        for ticker in tickers:
            try:
                price_data = get_prices(ticker, start_date, end_date, interval=interval)
                # logger.info(f"获取到 {ticker} 的价格数据: {len(price_data)} 条记录")
                
                # 检查price_data是否为列表
                if isinstance(price_data, list) and price_data:
                    # 处理Price对象列表，使用标准化的时间格式
                    stock_prices_for_record[ticker] = []
                    for price_obj in price_data:
                        try:
                            stock_prices_for_record[ticker].append({
                                "date": price_obj.time,  # 已经标准化的时间戳
                                "price": float(price_obj.close)
                            })
                        except Exception as e:
                            logger.error(f"处理价格对象时出错: {str(e)}, 对象: {price_obj}")
                    # logger.info(f"获取到 {ticker} 的价格数据(列表): {len(stock_prices_for_record[ticker])} 条记录")
                else:
                    logger.warning(f"无法获取 {ticker} 的价格数据或格式不支持: {type(price_data)}")
                    stock_prices_for_record[ticker] = []
            except Exception as e:
                logger.error(f"获取 {ticker} 价格数据时出错: {str(e)}")
                stock_prices_for_record[ticker] = []

        final_state = agent.invoke(
            {
                "messages": [
                    HumanMessage(
                        content="Make trading decisions based on the provided data.",
                    )
                ],
                "data": {
                    "tickers": tickers,
                    "portfolio": portfolio,
                    "start_date": start_date,
                    "end_date": end_date,
                    "interval": interval,
                    "interval_multiplier": interval_multiplier,
                    "analyst_signals": {},
                    "stock_prices": stock_prices_for_record,
                },
                "metadata": {
                    "show_reasoning": show_reasoning,
                    "model_name": model_name,
                    "model_provider": model_provider,
                },
            },
        )

        # 获取股票价格数据
        stock_prices = {}
        if 'stock_prices' in final_state["data"] and final_state["data"]["stock_prices"]:
            stock_prices = final_state["data"]["stock_prices"]
            logger.info(f"从final_state获取到股票价格数据: {len(stock_prices)} 只股票")
        else:
            # 如果没有股票价格数据，使用预先获取的数据
            stock_prices = stock_prices_for_record
            logger.info(f"使用预先获取的股票价格数据: {len(stock_prices)} 只股票")
            
            # 如果仍然没有数据，尝试从分析师信号中提取
            if not any(len(prices) > 0 for ticker, prices in stock_prices.items()):
                logger.info("尝试从分析师信号中提取股票价格数据")
                for ticker in tickers:
                    stock_prices[ticker] = []
                    for analyst, signals in final_state["data"]["analyst_signals"].items():
                        if ticker in signals and 'price_data' in signals[ticker]:
                            price_data = signals[ticker]['price_data']
                            if isinstance(price_data, dict) and 'date' in price_data and 'price' in price_data:
                                # 单个价格数据点
                                stock_prices[ticker].append({
                                    'date': price_data['date'],
                                    'price': price_data['price']
                                })
                                logger.info(f"从分析师 {analyst} 信号中提取到 {ticker} 的单个价格数据点")
                            elif isinstance(price_data, list):
                                # 价格数据列表
                                for item in price_data:
                                    if isinstance(item, dict) and 'date' in item and 'price' in item:
                                        stock_prices[ticker].append({
                                            'date': item['date'],
                                            'price': item['price']
                                        })
                                logger.info(f"从分析师 {analyst} 信号中提取到 {ticker} 的 {len(price_data)} 条价格数据")

        # 记录最终的股票价格数据
        for ticker, prices in stock_prices.items():
            logger.info(f"最终 {ticker} 的价格数据: {len(prices)} 条记录")

        return {
            "decisions": parse_hedge_fund_response(final_state["messages"][-1].content),
            "analyst_signals": final_state["data"]["analyst_signals"],
            "stock_prices": stock_prices
        }
    finally:
        # 停止进度跟踪
        progress.stop()

def start(state: AgentState):
    """使用输入消息初始化工作流。"""
    return state

def create_workflow(selected_analysts=None):
    """创建带有选定分析师的工作流。"""
    workflow = StateGraph(AgentState)
    workflow.add_node("start_node", start)

    # 从配置中获取分析师节点
    analyst_nodes = get_analyst_nodes()

    # 如果没有选择分析师，默认使用所有分析师
    if selected_analysts is None:
        selected_analysts = list(analyst_nodes.keys())
    # 添加选定的分析师节点
    for analyst_key in selected_analysts:
        node_name, node_func = analyst_nodes[analyst_key]
        workflow.add_node(node_name, node_func)
        workflow.add_edge("start_node", node_name)

    # 始终添加风险和投资组合管理
    workflow.add_node("risk_management_agent", risk_management_agent)
    workflow.add_node("portfolio_management_agent", portfolio_management_agent)

    # 将选定的分析师连接到风险管理
    for analyst_key in selected_analysts:
        node_name = analyst_nodes[analyst_key][0]
        workflow.add_edge(node_name, "risk_management_agent")

    workflow.add_edge("risk_management_agent", "portfolio_management_agent")
    workflow.add_edge("portfolio_management_agent", END)

    workflow.set_entry_point("start_node")
    return workflow