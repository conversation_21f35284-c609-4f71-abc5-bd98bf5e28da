from flask import render_template, jsonify, request
from flask_login import current_user
from auth.decorators import dev_login_required
from data.cache import get_cache
from config import APP_CONFIG, STOCK_TO_MONITOR
from datetime import datetime, timedelta
from task.task_service import preload_daily_data
from tools.api import get_prices, get_financial_metrics, get_company_news, get_insider_trades
import threading
import traceback

@dev_login_required
def data_monitor_view():
    """显示数据监控页面"""
    try:
        # 获取缓存实例
        cache = get_cache()
        
        # 获取监控的股票列表
        tickers = [code for code, _ in STOCK_TO_MONITOR]
        market = "US"  # 默认市场
        
        # 获取最后刷新时间
        last_refresh_time = cache.get_last_refresh_time() or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取各类数据的最新记录
        price_data = {}
        financial_data = {}
        news_data = {}
        insider_data = {}
        
        for ticker in tickers:
            # 获取最新价格数据
            prices = cache.get_prices(ticker, interval="day", interval_multiplier=1)
            if prices and len(prices) > 0:
                # 按时间排序并获取最新的3条
                sorted_prices = sorted(prices, key=lambda x: x.get('time', ''), reverse=True)
                price_data[ticker] = sorted_prices[:20]
            
            # 获取最新财务指标数据
            metrics = cache.get_financial_metrics(ticker)
            if metrics and len(metrics) > 0:
                # 按报告期排序并获取最新的3条
                sorted_metrics = sorted(metrics, key=lambda x: x.get('report_period', ''), reverse=True)
                financial_data[ticker] = sorted_metrics[:20]
            
            # 获取最新新闻数据
            news = cache.get_company_news(ticker)
            if news and len(news) > 0:
                # 按日期排序并获取最新的10条
                sorted_news = sorted(news, key=lambda x: x.get('date', ''), reverse=True)
                news_data[ticker] = sorted_news[:20]
            
            # 获取最新内部交易数据
            trades = cache.get_insider_trades(ticker)
            if trades and len(trades) > 0:
                valid_trades = [trade for trade in trades if trade.get('transaction_shares') and float(trade.get('transaction_shares', 0) or 0) != 0]
                # 按交易日期排序并获取最新的10条
                sorted_trades = sorted(valid_trades, key=lambda x: x.get('transaction_date') or x.get('filing_date') or '', reverse=True)
                insider_data[ticker] = sorted_trades[:20]
        
        # 获取定时任务配置信息
        preload_hour = 6  # 美国市场默认为6点
        timezone = "US/Eastern"  # 美国东部时间
        
        return render_template('data_monitor.html',
                              tickers=tickers,
                              market=market,
                              last_refresh_time=last_refresh_time,
                              price_data=price_data,
                              financial_data=financial_data,
                              news_data=news_data,
                              insider_data=insider_data,
                              preload_hour=preload_hour,
                              timezone=timezone,
                              user=current_user,
                              config={'dev_mode': APP_CONFIG['dev_mode']})
    except Exception as e:
        import traceback
        print(f"加载数据监控页面时出错: {str(e)}")
        print(traceback.format_exc())
        return render_template('error.html', error=str(e))

@dev_login_required
def refresh_all_data():
    """刷新所有数据"""
    try:
        # 获取监控的股票列表
        tickers = [code for code, _ in STOCK_TO_MONITOR]
        
        # 在后台线程中执行数据刷新，避免阻塞请求
        def background_refresh():
            try:
                # 调用预加载函数刷新所有数据
                preload_daily_data(tickers, force_refresh=True)
                
                # 更新最后刷新时间
                cache = get_cache()
                now = datetime.now()
                cache.set_last_refresh_time(now.strftime("%Y-%m-%d %H:%M:%S"))
            except Exception as e:
                print(f"后台刷新数据时出错: {str(e)}")
                print(traceback.format_exc())
        
        # 启动后台线程
        thread = threading.Thread(target=background_refresh)
        thread.daemon = True
        thread.start()
        
        return jsonify({"success": True, "message": "数据刷新任务已启动"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})

@dev_login_required
def refresh_specific_data():
    """刷新特定类型的数据"""
    try:
        data_type = request.form.get('data_type')
        if not data_type:
            return jsonify({"success": False, "message": "未指定数据类型"})
        
        # 获取监控的股票列表
        tickers = [code for code, _ in STOCK_TO_MONITOR]
        
        # 在后台线程中执行数据刷新，避免阻塞请求
        def background_refresh():
            try:
                now = datetime.now()
                end_date = now.strftime('%Y-%m-%d %H:%M:%S')
                start_date = (now - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')
                
                for ticker in tickers:
                    try:
                        if data_type == 'price':
                            # 刷新价格数据
                            get_prices(ticker, start_date, end_date, interval="day", interval_multiplier=1, force_refresh=True)
                            # 同时刷新分钟级数据
                            get_prices(ticker, end_date, end_date, interval="minute", interval_multiplier=1, force_refresh=True)
                        
                        elif data_type == 'financial':
                            # 刷新财务指标数据
                            get_financial_metrics(ticker, end_date, force_refresh=True)
                        
                        elif data_type == 'news':
                            # 刷新新闻数据
                            get_company_news(ticker, end_date, start_date, force_refresh=True)
                        
                        elif data_type == 'insider':
                            # 刷新内部交易数据
                            get_insider_trades(ticker, end_date, start_date, force_refresh=True)
                    
                    except Exception as e:
                        print(f"刷新 {ticker} 的 {data_type} 数据时出错: {str(e)}")
            
                
                # 更新最后刷新时间
                cache = get_cache()
                cache.set_last_refresh_time(now.strftime("%Y-%m-%d %H:%M:%S"))
            except Exception as e:
                print(f"后台刷新 {data_type} 数据时出错: {str(e)}")
                print(traceback.format_exc())
        
        # 启动后台线程
        thread = threading.Thread(target=background_refresh)
        thread.daemon = True
        thread.start()
        
        return jsonify({"success": True, "message": f"{data_type} 数据刷新任务已启动"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})

@dev_login_required
def get_last_refresh_time():
    """获取最后刷新时间"""
    try:
        cache = get_cache()
        last_refresh_time = cache.get_last_refresh_time()
        return jsonify({
            "success": True,
            "last_refresh_time": last_refresh_time
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": str(e)
        })