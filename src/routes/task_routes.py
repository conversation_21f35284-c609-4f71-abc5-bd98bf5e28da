from flask import render_template, redirect, url_for, flash, jsonify, request
from flask_login import current_user
from auth.decorators import dev_login_required
from task.task_queue import (
    get_all_tasks, 
    get_task_execution_history, 
    get_task_by_id, 
    pause_task_by_id, 
    resume_task_by_id, 
    delete_task_by_id,
    get_task_executions,
    get_task_stock_prices,
    ScheduledTask,
    TaskExecution
)
from data.database import SessionLocal, TradingRecord
from datetime import datetime, timedelta
import json
import html
from config import APP_CONFIG
from utils.analysts import ANALYST_NAMES

from utils.logger import get_logger
logger = get_logger(__name__)

@dev_login_required
def task_queue_view():
    """显示任务队列页面"""
    tasks = get_all_tasks()
    task_history = get_task_execution_history()
    
    return render_template('task_queue.html',
                          tasks=tasks,
                          task_history=task_history,
                          user=current_user,
                          config={'dev_mode': APP_CONFIG['dev_mode']})

def task_detail(task_id):
    """显示任务详情页面"""
    try:
        task = get_task_by_id(task_id)
        
        if not task:
            flash('任务不存在', 'danger')
            return redirect(url_for('task_queue'))
        
        # 获取任务执行历史
        executions = get_task_executions(task_id, days=7)

        # 处理 decisions 中可能包含的特殊字符
        for execution in executions:
            if 'decisions' in execution and execution['decisions']:
                for ticker, decision in execution['decisions'].items():
                    if 'reasoning' in decision and decision['reasoning']:
                        # 转义 reasoning 中的特殊字符
                        decision['reasoning'] = html.escape(decision['reasoning'])
        
        # 使用 ensure_ascii=False 确保非ASCII字符被正确处理
        executions_json = json.dumps(executions, ensure_ascii=False)
        
        # 获取股票价格数据
        stock_prices = get_task_stock_prices(task_id)
        
        return render_template('task_detail.html', 
                                task=task, 
                                executions=executions, 
                                executions_json=executions_json, 
                                stock_prices=json.dumps(stock_prices),
                                user=current_user,
                                analyst_names=ANALYST_NAMES,
                                config={'dev_mode': APP_CONFIG['dev_mode']})
    except Exception as e:
        import traceback
        logger.error(f"加载任务详情时出错: {str(e)}")
        logger.error(traceback.format_exc())
        flash(f'加载任务详情时出错: {str(e)}', 'danger')
        return redirect(url_for('task_queue'))

def get_task(task_id):
    """获取任务详情"""
    task = get_task_by_id(task_id)
    if task:
        return jsonify({"success": True, "task": task})
    else:
        return jsonify({"success": False, "message": "任务不存在"}), 404

def pause_task(task_id):
    """暂停任务"""
    success, message = pause_task_by_id(task_id)
    return jsonify({"success": success, "message": message})

def resume_task(task_id):
    """恢复任务"""
    success, message = resume_task_by_id(task_id)
    return jsonify({"success": success, "message": message})

def delete_task(task_id):
    """删除任务"""
    success, message = delete_task_by_id(task_id)
    return jsonify({"success": success, "message": message})

def check_running_tasks():
    """检查是否有正在运行的任务"""
    db = SessionLocal()
    try:
        # 检查状态为 active 的任务
        running_tasks = db.query(ScheduledTask).filter_by(status='active').count()
        return jsonify({
            'success': True,
            'has_running': running_tasks > 0
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })
    finally:
        db.close()

def check_task_completion():
    """检查是否有任务在最近完成"""
    db = SessionLocal()
    try:
        # 检查最近30秒内完成的任务执行记录
        recent_time = datetime.now() - timedelta(seconds=30)
        recent_executions = db.query(TaskExecution)\
            .filter(TaskExecution.execution_time >= recent_time)\
            .count()
        
        return jsonify({
            'success': True,
            'has_completion': recent_executions > 0
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })
    finally:
        db.close()

def check_backtest_status(record_id):
    """检查回测状态"""
    try:
        db = SessionLocal()
        record = db.query(TradingRecord).filter_by(id=record_id).first()
        
        if not record:
            return jsonify({
                'success': False,
                'message': '找不到回测记录'
            })
        
        # 检查回测是否完成
        metrics = record.metrics
        is_completed = metrics.get('status') != 'running'
        
        return jsonify({
            'success': True,
            'completed': is_completed,
            'redirect_url': url_for('history', record_id=record_id) if is_completed else None
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })
    finally:
        db.close()