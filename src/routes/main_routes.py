from flask import render_template, redirect, url_for, flash, request
from data.database import SessionLocal, TradingRecord
from utils.analysts import ANALYST_ORDER, ANALYST_NAMES
from llm.models import LLM_ORDER, DEFAULT_MODEL
from config import STOCK_OPTIONS, DEFAULT_STOCK, APP_CONFIG
from task.task_queue import get_all_tasks
from auth.decorators import dev_login_required
from flask_login import current_user
import json

@dev_login_required
def index():
    """主页 - 显示交易界面和历史记录"""
    # 获取最近的交易记录
    db = SessionLocal()
    recent_trades = db.query(TradingRecord).order_by(TradingRecord.timestamp.desc()).limit(10).all()
    db.close()
    
    # 准备分析师和模型选项
    analysts = [{"display": display, "value": value} for display, value in ANALYST_ORDER]
    models = [{"display": display, "value": value} for display, value, _ in LLM_ORDER]
    
    # 准备股票选项
    stocks = [{"value": code, "display": f"{code} - {name}"} for code, name in STOCK_OPTIONS]
    
    # 获取活跃任务数量
    active_tasks = len([task for task in get_all_tasks() if task['status'] == 'active'])
    
    return render_template('index.html', 
                         analysts=analysts, 
                         models=models,
                         stocks=stocks,
                         default_stock=DEFAULT_STOCK,
                         default_model=DEFAULT_MODEL,
                         recent_trades=recent_trades,
                         active_tasks=active_tasks,
                         user=current_user,
                         config={'dev_mode': APP_CONFIG['dev_mode']})

@dev_login_required
def history():
    """显示历史记录"""
    db = SessionLocal()
    records = db.query(TradingRecord).order_by(TradingRecord.timestamp.desc()).all()
    
    # 将记录转换为字典列表，以便在模板中更容易处理
    records_list = []
    for record in records:
        record_dict = record.__dict__.copy()
        
        # 移除SQLAlchemy的内部属性
        if '_sa_instance_state' in record_dict:
            record_dict.pop('_sa_instance_state')
            
        # 确保metrics是字典格式
        if record_dict.get('metrics') and not isinstance(record_dict['metrics'], dict):
            try:
                record_dict['metrics'] = json.loads(record_dict['metrics'])
            except:
                record_dict['metrics'] = {}
                
        # 确保total_return是浮点数
        if record_dict.get('metrics') and 'total_return' in record_dict['metrics']:
            try:
                total_return = float(record_dict['metrics']['total_return'])
                record_dict['metrics']['total_return'] = total_return
            except (ValueError, TypeError):
                record_dict['metrics']['total_return'] = None
                
        records_list.append(record_dict)
    
    db.close()
    
    return render_template('history.html',
                          records=records_list,
                          user=current_user,
                          analyst_names=ANALYST_NAMES,
                          config={'dev_mode': APP_CONFIG['dev_mode']})

def view_record(record_id):
    """查看单个交易记录"""
    db = SessionLocal()
    record = db.query(TradingRecord).filter_by(id=record_id).first()
    db.close()
    
    if not record:
        flash('记录不存在', 'danger')
        return redirect(url_for('history'))
    
    # 检查是否是回测记录
    if record.type == 'backtest':
        # 检查回测状态
        metrics = record.metrics or {}
        status = metrics.get('status', 'unknown')
        
        if status == 'running':
            # 如果回测仍在进行中，显示等待页面
            return render_template('backtest_running.html', 
                                 record_id=record_id,
                                 from_page=request.args.get('from_page'),
                                 task_id=request.args.get('task_id'))
        
        # 回测已完成或失败，显示结果
        portfolio_values_json = json.dumps(record.portfolio_values) if isinstance(record.portfolio_values, list) else record.portfolio_values
        
        # 从记录中获取交易历史和每日摘要数据
        trade_history = record.trade_history or []
        daily_summaries = record.daily_summaries or []
        
        # 获取股票价格数据，如果不存在则提供空对象
        stock_prices = {}
        daily_decisions = {}
        daily_analyst_signals = {}
        
        if metrics:
            if 'stock_prices' in metrics:
                stock_prices = metrics['stock_prices']
            if 'daily_decisions' in metrics:
                daily_decisions = metrics['daily_decisions']
            if 'daily_analyst_signals' in metrics:
                daily_analyst_signals = metrics['daily_analyst_signals']
        
        # 使用记录中的字段，如果不存在则使用metrics中的数据
        if record.daily_decisions:
            daily_decisions = record.daily_decisions
        if record.daily_analyst_signals:
            daily_analyst_signals = record.daily_analyst_signals
        
        stock_prices_json = json.dumps(stock_prices)
        
        daily_decisions_json = json.dumps(daily_decisions, ensure_ascii=False).replace("'", "\\'")
        daily_analyst_signals_json = json.dumps(daily_analyst_signals, ensure_ascii=False).replace("'", "\\'")

        return render_template('backtest_results.html',
                                metrics=metrics,
                                tickers=record.tickers,
                                portfolio_values=portfolio_values_json,
                                stock_prices=stock_prices_json,
                                daily_decisions=daily_decisions_json,
                                daily_analyst_signals=daily_analyst_signals_json,
                                execution_log=record.execution_log,
                                trade_history=trade_history,
                                daily_summaries=daily_summaries,
                                record_id=record_id,
                                from_page=request.args.get('from_page'),
                                task_id=request.args.get('task_id'),
                                analyst_names=ANALYST_NAMES)
    else:
        # 交易记录
        result = {
                'decisions': record.decisions or {},
                'analyst_signals': record.analyst_signals or {}
            }
        # 获取股票价格数据
        stock_prices = {}
        if record.metrics and isinstance(record.metrics, dict) and 'stock_prices' in record.metrics:
            stock_prices = record.metrics['stock_prices']
        
        # 如果没有股票价格数据，创建空对象
        if not stock_prices:
            stock_prices = {ticker: [] for ticker in record.tickers}
            
        return render_template('results.html',
                                result=result,
                                tickers=record.tickers,
                                result_json=json.dumps(result, ensure_ascii=False),
                                execution_log=record.execution_log,
                                record_id=record.id,
                                analyst_names=ANALYST_NAMES,
                                trading_date=record.trading_date,
                                stock_prices=json.dumps(stock_prices),
                                from_page=request.args.get('from_page'),
                                task_id=request.args.get('task_id'))

def logout():
    """Compatibility route, redirect to auth.logout"""
    return redirect(url_for('auth.logout'))
