from routes.main_routes import index, history, view_record, logout
from routes.trading_routes import run_trading, run_backtest
from routes.task_routes import (
    task_queue_view, task_detail, get_task, pause_task, 
    resume_task, delete_task, check_running_tasks, check_task_completion
)
from routes.data_routes import data_monitor_view, refresh_all_data, refresh_specific_data, get_last_refresh_time

def register_routes(app):
    """Register all routes with the Flask app"""
    # Main UI routes
    app.add_url_rule('/', 'index', index)
    app.add_url_rule('/history', 'history', history)
    app.add_url_rule('/record/<int:record_id>', 'view_record', view_record)
    app.add_url_rule('/logout', 'logout', logout)
    
    # Trading and backtest routes
    app.add_url_rule('/run_trading', 'run_trading', run_trading, methods=['POST'])
    app.add_url_rule('/run_backtest', 'run_backtest', run_backtest, methods=['POST'])
    
    # Task management routes
    app.add_url_rule('/task_queue', 'task_queue', task_queue_view)
    app.add_url_rule('/task/<task_id>', 'task_detail', task_detail)
    app.add_url_rule('/api/tasks/<task_id>', 'get_task', get_task)
    app.add_url_rule('/api/tasks/<task_id>/pause', 'pause_task', pause_task, methods=['POST'])
    app.add_url_rule('/api/tasks/<task_id>/resume', 'resume_task', resume_task, methods=['POST'])
    app.add_url_rule('/api/tasks/<task_id>', 'delete_task', delete_task, methods=['DELETE'])
    app.add_url_rule('/api/tasks/has_running', 'check_running_tasks', check_running_tasks)
    app.add_url_rule('/api/tasks/check_completion', 'check_task_completion', check_task_completion)
    
    # Data monitoring routes
    app.add_url_rule('/data_monitor', 'data_monitor', data_monitor_view)
    app.add_url_rule('/api/data/refresh_all', 'refresh_all_data', refresh_all_data, methods=['POST'])
    app.add_url_rule('/api/data/refresh', 'refresh_specific_data', refresh_specific_data, methods=['POST'])
    app.add_url_rule('/api/data/last_refresh_time', 'get_last_refresh_time', get_last_refresh_time, methods=['GET'])