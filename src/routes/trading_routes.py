from flask import request, render_template
from datetime import datetime
from dateutil.relativedelta import relativedelta
import pytz
from task.trading_service import run_trading_operation
from task.backtest_service import run_backtest_task
from data.database import SessionLocal, TradingRecord
from utils.analysts import ANALYST_NAMES
from utils.json_utils import sanitize_json_data
from llm.models import get_model_info
import threading

def run_trading():
    """处理交易表单提交"""
    data = request.form
    action = data.get('action', 'run_now')
    
    # 获取表单数据
    tickers = request.form.getlist('tickers[]')
    if not tickers: 
        tickers = [ticker.strip() for ticker in data.get('tickers', '').split(',')]
    selected_analysts = request.form.getlist('analysts')
    model_choice = data.get('model')
    
    # 处理交易日期，确保包含时间信息
    trading_date = data.get('trading_date')
    if trading_date:
        try:
            # 将字符串转换为datetime对象
            trading_date = datetime.strptime(trading_date, '%Y-%m-%dT%H:%M')
        except ValueError:
            # 如果转换失败，使用当前时间
            trading_date = datetime.now()
    else:
        trading_date = datetime.now()
    initial_cash = float(data.get('initial_cash', 100000.0))
    margin_requirement = float(data.get('margin_requirement', 0.0))
    show_reasoning = 'show_reasoning' in data
    interval = 'minute'
    interval_multiplier = 1
    execution_time_start = request.form.get('execution_time_start', '21:30')
    execution_time_end = request.form.get('execution_time_end', '04:30')
    
    # 创建初始记录
    db = SessionLocal()
    # 将交易日期转换为UTC时间
    utc_trading_date = trading_date.astimezone(pytz.UTC) if trading_date.tzinfo else pytz.UTC.localize(trading_date)
    
    record = TradingRecord(
        type='trading',
        tickers=tickers,
        selected_analysts=selected_analysts,
        model_choice=model_choice,
        initial_cash=initial_cash,
        margin_requirement=margin_requirement,
        trading_date=utc_trading_date,
        show_reasoning=show_reasoning,
        decisions=sanitize_json_data({}),
        analyst_signals=sanitize_json_data({}),
        execution_log="交易正在进行中...",
        metrics=sanitize_json_data({"status": "running"}),
        portfolio_values=sanitize_json_data([])
    )
    db.add(record)
    db.commit()
    record_id = record.id
    db.close()
    
    # 如果是加入队列
    if action == 'add_to_queue' and data.get('enable_schedule') == 'on':
        # 准备任务数据
        task_data = {
            'task_name': data.get('task_name', f"交易任务 - {', '.join(tickers)}"),
            'tickers': ','.join(tickers),
            'selected_analysts': selected_analysts,
            'model_choice': model_choice,
            'trading_date': trading_date,
            'initial_cash': initial_cash,
            'margin_requirement': margin_requirement,
            'show_reasoning': show_reasoning,
            'interval': interval,
            'interval_multiplier': interval_multiplier,
            'execution_interval': data.get('execution_interval', '5m'),
            'execution_time_start': execution_time_start,
            'execution_time_end': execution_time_end,
            'end_condition': data.get('end_condition', 'never'),
            'end_count': int(data.get('end_count', 10)) if data.get('end_condition') == 'count' else None,
            'end_date': data.get('end_date') if data.get('end_condition') == 'date' else None,
        }
        
        # 添加任务到队列
        from task.task_queue import add_task
        success, task_id = add_task(task_data)
        
        if success:
            # 检查是否需要立即执行一次
            execute_immediately = 'execute_immediately' in data
            
            if execute_immediately:
                # 启动后台线程执行交易
                trading_thread = threading.Thread(
                    target=run_trading_operation,
                    args=(tickers, selected_analysts, model_choice, trading_date, 
                          initial_cash, margin_requirement, show_reasoning, 
                          'minute', 1, True, task_id, record_id),
                    daemon=True
                )
                trading_thread.start()
            
            return render_template('task_added.html', 
                                  task_name=task_data['task_name'],
                                  task_id=task_id,
                                  record_id=record_id if execute_immediately else None)
        else:
            return render_template('error.html', 
                                  error_message=f"添加任务失败: {task_id}")
    
    # 立即执行交易
    trading_thread = threading.Thread(
        target=run_trading_operation,
        args=(tickers, selected_analysts, model_choice, trading_date, 
              initial_cash, margin_requirement, show_reasoning, 
              'minute', 1, False, None, record_id),
        daemon=True
    )
    trading_thread.start()
    
    # 返回提示页面
    return render_template('trading_submitted.html', record_id=record_id)

def run_backtest():
    """处理回测表单提交"""
    data = request.form
    
    # 获取表单数据
    tickers = request.form.getlist('tickers[]')
    if not tickers:
        tickers = [ticker.strip() for ticker in data.get('tickers', '').split(',')]
    selected_analysts = request.form.getlist('analysts')
    model_choice = data.get('model')
    
    # 处理日期
    use_default_period = 'use_default_period' in data
    if use_default_period:
        # 使用默认时间段，从表单获取
        start_date = data.get('default_start_date', '2025-04-10')
        end_date = data.get('default_end_date', '2025-05-10')
    else:
        # 使用用户指定的日期
        start_date = data.get('start_date')
        end_date = data.get('end_date') or datetime.now().strftime('%Y-%m-%d')
        
        # 如果没有提供开始日期，默认为结束日期前1个月
        if not start_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            start_date = (end_date_obj - relativedelta(months=1)).strftime('%Y-%m-%d')
    
    initial_capital = float(data.get('initial_cash', 100000.0))
    margin_requirement = float(data.get('margin_requirement', 0.0))
    show_reasoning = 'show_reasoning' in data
    force_refresh = 'force_refresh' in data
    
    # 获取模型提供商
    model_info = get_model_info(model_choice)
    model_provider = model_info.provider.value if model_info else "未知"
    
    # 创建一个记录，标记为"进行中"
    db = SessionLocal()
    record = TradingRecord(
        type='backtest',
        tickers=tickers,
        selected_analysts=selected_analysts,
        model_choice=model_choice,
        initial_cash=initial_capital,
        margin_requirement=margin_requirement,
        start_date=start_date,
        end_date=end_date,
        show_reasoning=show_reasoning,
        decisions=sanitize_json_data({}),  # 回测没有单一决策
        analyst_signals=sanitize_json_data({}),  # 回测没有单一分析师信号
        execution_log="回测正在进行中...",
        metrics=sanitize_json_data({"status": "running"}),
        portfolio_values=sanitize_json_data([])
    )
    db.add(record)
    db.commit()
    record_id = record.id
    db.close()
    
    # 启动后台线程执行回测
    backtest_thread = threading.Thread(
        target=run_backtest_task,
        args=(record_id, tickers, selected_analysts, model_choice, model_provider, 
              start_date, end_date, initial_capital, margin_requirement, show_reasoning, force_refresh),
        daemon=True
    )
    backtest_thread.start()
    
    # 返回提示页面
    return render_template('backtest_submitted.html', record_id=record_id)
