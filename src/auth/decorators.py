from functools import wraps
from flask_login import login_required, current_user, login_user
from data.database import SimpleUser
from config import APP_CONFIG

def dev_login_required(func):
    """自定义登录要求装饰器，开发模式下跳过验证"""
    @wraps(func)
    def decorated_view(*args, **kwargs):
        if APP_CONFIG['dev_mode']:
            # 开发模式下，如果用户未登录，自动创建一个开发者用户
            if not current_user.is_authenticated:
                dev_user = SimpleUser('dev_user', 'Developer')
                login_user(dev_user)
            return func(*args, **kwargs)
        return login_required(func)(*args, **kwargs)
    return decorated_view