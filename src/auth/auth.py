from flask import Blueprint, redirect, url_for, session, request, flash
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, current_user
from authlib.integrations.flask_client import OAuth
from urllib.parse import urlencode
import secrets
from datetime import datetime
from data.database import SimpleUser
from config import AUTHELIA_CONFIG

# 创建蓝图
auth_bp = Blueprint('auth', __name__)

# 初始化OAuth
oauth = OAuth()

# 创建LoginManager实例
login_manager = LoginManager()

def init_auth(app):
    """初始化认证模块"""
    # 初始化Flask-Login
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    
    # 初始化OAuth
    oauth.init_app(app)
    
    # 注册蓝图
    app.register_blueprint(auth_bp)
    
    # 配置OIDC客户端
    setup_oidc()
    
    return login_manager

def setup_oidc():
    """配置OIDC客户端"""
    oauth.register(
        name='authelia',
        client_id=AUTHELIA_CONFIG['client_id'],
        client_secret=AUTHELIA_CONFIG['client_secret'],
        server_metadata_url=f"{AUTHELIA_CONFIG['url']}/.well-known/openid-configuration",
        client_kwargs={
            'scope': AUTHELIA_CONFIG['scope'],
            'token_endpoint_auth_method': AUTHELIA_CONFIG['token_endpoint_auth_method'],
        },
    )

@login_manager.user_loader
def load_user(user_id):
    """从会话中加载用户"""
    if 'user_id' in session and session.get('user_id') == user_id:
        username = session.get('username', 'User')
        user = SimpleUser(user_id, username)
        return user
    return None

@auth_bp.route('/login')
def login():
    """显示登录页面或重定向到Authelia"""
    print(f"Login route called at {datetime.now().strftime('%H:%M:%S')}")
    
    if current_user.is_authenticated:
        print("User already authenticated, redirecting to index")
        next_url = request.args.get('next')
        if next_url and next_url.startswith('http'):
            return redirect(next_url)
        return redirect(url_for('index'))
    
    next_url = request.args.get('next', url_for('index'))
    if next_url.startswith('http'):
        pass
    elif not next_url.startswith('/'):
        next_url = '/' + next_url
    
    state = secrets.token_urlsafe(32)
    nonce = secrets.token_urlsafe(32)
    session['oauth_state'] = state
    session['oauth_nonce'] = nonce
    session['next'] = next_url
    session.modified = True
    
    redirect_uri = url_for('auth.auth_callback', _external=True)
    
    if redirect_uri not in AUTHELIA_CONFIG['redirect_uris']:
        redirect_uri = AUTHELIA_CONFIG['redirect_uris'][0]
        if redirect_uri not in AUTHELIA_CONFIG['redirect_uris']:
            AUTHELIA_CONFIG['redirect_uris'].append(redirect_uri)
    
    return oauth.authelia.authorize_redirect(
        redirect_uri=redirect_uri, 
        state=state,
        nonce=nonce,
        scope=AUTHELIA_CONFIG['scope']
    )

@auth_bp.route('/auth/callback')
def auth_callback():
    """处理Authelia OIDC回调"""
    print(f"Auth callback route called at {datetime.now().strftime('%H:%M:%S')}")
    
    if 'oauth_state' not in session or session['oauth_state'] != request.args.get('state'):
        print(f"Invalid state. Session state: {session.get('oauth_state')}, Request state: {request.args.get('state')}")
        flash('无效的认证状态', 'danger')
        return redirect(url_for('auth.login'))
    
    try:
        token = oauth.authelia.authorize_access_token()
        nonce = session.get('oauth_nonce')
        userinfo = oauth.authelia.parse_id_token(token, nonce=nonce)
        
        session.pop('oauth_nonce', None)
        
        if not userinfo:
            raise ValueError("无法获取用户信息")
        
        user_id = str(hash(userinfo['sub']))
        username = userinfo.get('preferred_username') or userinfo.get('name', 'Ying')
        
        session['user_id'] = user_id
        session['username'] = username
        session['email'] = userinfo.get('email')
        session['authenticated'] = True
        
        user = SimpleUser(user_id, username)
        login_user(user, remember=True)
        
        next_page = session.pop('next', None) or request.args.get('next')
        session.pop('oauth_state', None)
        
        if next_page and next_page.startswith('http'):
            session.modified = True
            return redirect(next_page)
        
        if not next_page or not next_page.startswith('/'):
            next_page = url_for('index')
        
        session.modified = True
        return redirect(next_page)
        
    except Exception as e:
        print(f"认证回调处理异常: {str(e)}")
        flash(f'认证失败: {str(e)}', 'danger')
        return redirect(url_for('auth.login'))

@auth_bp.route('/logout')
def logout():
    """用户注销"""
    post_logout_redirect_uri = AUTHELIA_CONFIG['logout_redirect']
    session.clear()
    logout_user()
    
    params = {'rd': post_logout_redirect_uri}
    logout_url = f"{AUTHELIA_CONFIG['url']}/logout?{urlencode(params)}"
    return redirect(logout_url)