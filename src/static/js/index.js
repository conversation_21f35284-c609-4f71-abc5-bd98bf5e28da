// 初始化Select2
$(document).ready(function() {
    // 交易表单的股票代码选择器
    $('#tickers').select2({
        tags: true,
        tokenSeparators: [',', ' '],
        placeholder: "选择或输入股票代码",
        allowClear: true,
        width: '100%'
    });
    
    // 默认选中配置的默认股票
    $('#tickers').val([defaultStock]).trigger('change');
    
    // 回测表单的股票代码选择器
    $('#backtest-tickers').select2({
        tags: true,
        tokenSeparators: [',', ' '],
        placeholder: "选择或输入股票代码",
        allowClear: true,
        width: '100%'
    });
    
    // 默认选中配置的默认股票
    $('#backtest-tickers').val([defaultStock]).trigger('change');
});

// 全选/取消全选分析师
document.getElementById('select-all-analysts').addEventListener('click', function() {
    document.querySelectorAll('#trading input[name="analysts"]').forEach(checkbox => {
        checkbox.checked = true;
    });
});

document.getElementById('deselect-all-analysts').addEventListener('click', function() {
    document.querySelectorAll('#trading input[name="analysts"]').forEach(checkbox => {
        checkbox.checked = false;
    });
});

document.getElementById('backtest-select-all-analysts').addEventListener('click', function() {
    document.querySelectorAll('#backtest input[name="analysts"]').forEach(checkbox => {
        checkbox.checked = true;
    });
});

document.getElementById('backtest-deselect-all-analysts').addEventListener('click', function() {
    document.querySelectorAll('#backtest input[name="analysts"]').forEach(checkbox => {
        checkbox.checked = false;
    });
});

// 添加表单验证和提交状态控制
document.querySelector('#trading form').addEventListener('submit', function(event) {
    const selectedAnalysts = document.querySelectorAll('#trading input[name="analysts"]:checked');
    const submitBtn = document.getElementById('trading-submit-btn');
    
    if (selectedAnalysts.length === 0) {
        event.preventDefault();
        alert('请至少选择一个分析师');
        return;
    }
    
    // 设置按钮为加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';
});

document.querySelector('#backtest form').addEventListener('submit', function(event) {
    const selectedAnalysts = document.querySelectorAll('#backtest input[name="analysts"]:checked');
    const submitBtn = document.getElementById('backtest-submit-btn');
    
    if (selectedAnalysts.length === 0) {
        event.preventDefault();
        alert('请至少选择一个分析师');
        return;
    }
    
    // 设置按钮为加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';
});

// 设置日期默认值
const today = new Date();
const oneMonthAgo = new Date();
oneMonthAgo.setMonth(today.getMonth() - 1);

const formatDate = date => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const formatDateTime = date => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// 确保元素存在后再设置值
if (document.getElementById('trading_date')) {
    document.getElementById('trading_date').value = formatDateTime(today);
}
if (document.getElementById('backtest-start_date')) {
    document.getElementById('backtest-start_date').value = formatDate(oneMonthAgo);
}
if (document.getElementById('backtest-end_date')) {
    document.getElementById('backtest-end_date').value = formatDate(today);
}

// 定时执行相关的控制逻辑
document.getElementById('enable_schedule').addEventListener('change', function() {
    const scheduleOptions = document.getElementById('schedule-options');
    const queueBtn = document.getElementById('trading-queue-btn');
    
    if (this.checked) {
        scheduleOptions.style.display = 'block';
        queueBtn.style.display = 'inline-block';
    } else {
        scheduleOptions.style.display = 'none';
        queueBtn.style.display = 'none';
    }
});

document.getElementById('end_condition').addEventListener('change', function() {
    const endCount = document.getElementById('end_count');
    const endDate = document.getElementById('end_date');
    
    endCount.style.display = 'none';
    endDate.style.display = 'none';
    
    if (this.value === 'count') {
        endCount.style.display = 'block';
    } else if (this.value === 'date') {
        endDate.style.display = 'block';
        // 设置默认日期为一周后
        const oneWeekLater = new Date();
        oneWeekLater.setDate(oneWeekLater.getDate() + 7);
        endDate.value = formatDate(oneWeekLater);
    }
});

// 修改表单提交逻辑，确保定时任务的必填字段已填写
document.querySelector('#trading form').addEventListener('submit', function(event) {
    const selectedAnalysts = document.querySelectorAll('#trading input[name="analysts"]:checked');
    const submitBtn = document.getElementById('trading-submit-btn');
    const queueBtn = document.getElementById('trading-queue-btn');
    const enableSchedule = document.getElementById('enable_schedule');
    const action = event.submitter ? event.submitter.value : 'run_now';
    
    if (selectedAnalysts.length === 0) {
        event.preventDefault();
        alert('请至少选择一个分析师');
        return;
    }
    
    // 如果是加入队列操作，检查定时任务设置
    if (action === 'add_to_queue') {
        if (!enableSchedule.checked) {
            event.preventDefault();
            alert('请启用定时执行');
            return;
        }

        // 验证执行时段
        const startTime = document.getElementById('execution_time_start').value;
        const endTime = document.getElementById('execution_time_end').value;
        
        if (!startTime || !endTime) {
            event.preventDefault();
            alert('请设置执行时段');
            return;
        }
        
        // 转换为分钟数进行比较
        const startMinutes = timeToMinutes(startTime);
        const endMinutes = timeToMinutes(endTime);
        
        // 如果开始时间大于结束时间，说明是跨日执行（例如21:30-04:30）
        if (startMinutes > endMinutes) {
            // 允许跨日执行
        } else if (startMinutes === endMinutes) {
            event.preventDefault();
            alert('开始时间不能等于结束时间');
            return;
        }
        
        const endCondition = document.getElementById('end_condition').value;
        if (endCondition === 'count' && document.getElementById('end_count').value <= 0) {
            event.preventDefault();
            alert('执行次数必须大于0');
            return;
        } else if (endCondition === 'date') {
            const endDate = new Date(document.getElementById('end_date').value);
            const today = new Date();
            if (endDate <= today) {
                event.preventDefault();
                alert('结束日期必须在今天之后');
                return;
            }
        }
        
        // 确保表单中有一个隐藏字段来传递action
        let actionInput = document.querySelector('input[name="action"]');
        if (!actionInput) {
            actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            this.appendChild(actionInput);
        }
        actionInput.value = action;
    }
    
    // 设置按钮为加载状态
    if (action === 'run_now') {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...';
    } else {
        queueBtn.disabled = true;
        queueBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>添加中...';
    }
});



// 设置默认的执行时段
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('execution_time_start');
    const endTimeInput = document.getElementById('execution_time_end');
    
    if (startTimeInput && !startTimeInput.value) {
        startTimeInput.value = '21:30';
    }
    if (endTimeInput && !endTimeInput.value) {
        endTimeInput.value = '04:30';
    }


    // Task queue button placeholder handler
    const tradingQueueBtn = document.getElementById('trading-queue-btn');
    if (tradingQueueBtn) {
        tradingQueueBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('task_queue') }}";
        });
    }
});

// timeToMinutes函数
function timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
}

// 处理默认时间段选项
document.getElementById('backtest-use-default-period').addEventListener('change', function() {
    const dateInputs = document.getElementById('backtest-date-inputs');
    const startDateInput = document.getElementById('backtest-start_date');
    const endDateInput = document.getElementById('backtest-end_date');
    
    if (this.checked) {
        // 如果选中，隐藏日期输入区域
        dateInputs.style.display = 'none';
        
        // 设置默认日期值
        startDateInput.value = '2025-04-10';
        endDateInput.value = '2025-05-10';
    } else {
        // 如果未选中，显示日期输入区域
        dateInputs.style.display = 'flex';
        
        // 恢复默认值
        startDateInput.value = formatDate(oneMonthAgo);
        endDateInput.value = formatDate(today);
    }
});

// 保存和恢复标签页状态
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有标签页按钮
    const tabButtons = document.querySelectorAll('button[data-bs-toggle="tab"]');
    
    // 从本地存储中获取上次选择的标签页
    const activeTabId = localStorage.getItem('activeIndexTab');
    
    // 如果有存储的标签页ID，则激活该标签页
    if (activeTabId) {
        const tabToActivate = document.querySelector(activeTabId);
        if (tabToActivate) {
            const tab = new bootstrap.Tab(tabToActivate);
            tab.show();
        }
    }
    
    // 为每个标签页按钮添加点击事件，保存选择到本地存储
    tabButtons.forEach(function(button) {
        button.addEventListener('click', function(event) {
            localStorage.setItem('activeIndexTab', '#' + event.target.id);
        });
    });
});
