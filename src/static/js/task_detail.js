// 初始化所有工具提示
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            html: true
        })
    })
    
    // 初始化所有popover
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true,
            placement: 'auto'
        })
    })
    
    // 点击其他地方关闭所有popover
    document.addEventListener('click', function(e) {
        if (!e.target.matches('[data-bs-toggle="popover"]')) {
            popoverList.forEach(function(popover) {
                popover.hide();
            });
        }
    });
    
    // 绑定暂停任务按钮事件
    const pauseTaskBtn = document.getElementById('pauseTaskBtn');
    if (pauseTaskBtn) {
        pauseTaskBtn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            pauseTask(taskId);
        });
    }
    
    // 绑定恢复任务按钮事件
    const resumeTaskBtn = document.getElementById('resumeTaskBtn');
    if (resumeTaskBtn) {
        resumeTaskBtn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            resumeTask(taskId);
        });
    }
    
    // 绑定删除任务按钮事件
    const deleteTaskBtn = document.getElementById('deleteTaskBtn');
    if (deleteTaskBtn) {
        deleteTaskBtn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            deleteTask(taskId);
        });
    }
    
    // 填充执行价格
    updateExecutionPrices();

    // 添加时间筛选按钮事件
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            // 更新按钮状态
            document.querySelectorAll('[data-filter]').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // 更新筛选状态
            currentTimeFilter = this.getAttribute('data-filter');
            
            // 更新图表和执行记录
            updateStockPriceChart();
            updateExecutionHistory();
        });
    });

    // 初始化执行记录显示
    updateExecutionHistory();
});

// 添加时间筛选变量
let currentTimeFilter = 'hour'; // 默认显示数据

// 查找最接近执行时间的价格点的公共函数
function findClosestPricePoint(ticker, executionTime) {
    if (!stockPricesData[ticker] || stockPricesData[ticker].length === 0) {
        return null;
    }
    
    const stockData = stockPricesData[ticker];
    const dates = stockData.map(item => item.date);
    const prices = stockData.map(item => item.price);
    
    let closestIndex = 0;
    let minTimeDiff = Infinity;
    
    dates.forEach((date, index) => {
        const timeDiff = Math.abs(new Date(date) - new Date(executionTime));
        if (timeDiff < minTimeDiff) {
            minTimeDiff = timeDiff;
            closestIndex = index;
        }
    });
    
    // 如果最近的时间点距离执行时间超过5分钟，则返回null
    const maxTimeDiffMs = 5 * 60 * 1000; // 5分钟的毫秒数
    const closestTimeDiff = Math.abs(new Date(dates[closestIndex]) - new Date(executionTime));
    if (closestTimeDiff > maxTimeDiffMs) {
        return null;
    }
    
    return {
        date: dates[closestIndex],
        price: prices[closestIndex],
        index: closestIndex
    };
}

// 更新执行价格的函数
function updateExecutionPrices() {
    document.querySelectorAll('.execution-price').forEach(element => {
        const ticker = element.getAttribute('data-ticker');
        const executionTime = element.getAttribute('data-time');
        const pricePoint = findClosestPricePoint(ticker, executionTime);
        
        if (pricePoint) {
            element.textContent = '$' + pricePoint.price.toFixed(2);
            
            // 根据操作类型添加颜色样式
            const row = element.closest('tr');
            const actionCell = row.querySelector('td:nth-child(2)');
            
            if (actionCell.querySelector('.action-buy, .action-cover')) {
                element.classList.add('text-success');
            } else if (actionCell.querySelector('.action-sell, .action-short')) {
                element.classList.add('text-danger');
            } else if (actionCell.querySelector('.action-hold')) {
                element.classList.add('text-secondary'); // 持有操作使用灰色
            } else if (actionCell.querySelector('.action-custom1')) {
                element.classList.add('text-warning');
            } else if (actionCell.querySelector('.action-custom2')) {
                element.classList.add('text-info');
            }
        } else {
            element.textContent = '-';
            element.classList.remove('text-success', 'text-danger');
        }
    });
}

// 解析股票价格数据
let stockPricesData = {};
try {
    // console.log("原始股票价格数据字符串:", stockPricesDataStr);
    
    if (stockPricesDataStr === '' || stockPricesDataStr === '{}') {
        console.warn("股票价格数据为空");
        stockPricesData = {};
    } else {
        stockPricesData = JSON.parse(stockPricesDataStr);
        
        if (typeof stockPricesData !== 'object' || stockPricesData === null) {
            console.error("解析后的股票价格数据无效");
            stockPricesData = {};
        }
    }
} catch (e) {
    console.error("解析股票价格数据时出错:", e);
    stockPricesData = {};
}

// 解析执行历史数据，用于在图表上标注操作点
let executionHistoryData = [];
try {
    // 使用一个更安全的方式解析JSON
    let executionsJsonStr = executionsJsonData;
    
    // 处理可能导致解析错误的特殊字符
    executionsJsonStr = executionsJsonStr.replace(/\n/g, '\\n')
                                       .replace(/\r/g, '\\r')
                                       .replace(/\\'/g, "\\\\'")
                                       .replace(/'/g, "\\'");
    
    // 尝试解析JSON
    const executionsData = JSON.parse(executionsJsonStr);
    executionHistoryData = executionsData.map(execution => {
        // 确保decisions是一个对象
        let decisions = execution.decisions || {};
        
        // 如果decisions是字符串，尝试解析它
        if (typeof decisions === 'string') {
            try {
                decisions = JSON.parse(decisions);
            } catch (e) {
                console.error("解析决策数据字符串时出错:", e);
                decisions = {};
            }
        }
        
        // 确保analyst_signals是一个对象
        let analyst_signals = execution.analyst_signals || {};
        
        // 如果analyst_signals是字符串，尝试解析它
        if (typeof analyst_signals === 'string') {
            try {
                analyst_signals = JSON.parse(analyst_signals);
            } catch (e) {
                console.error("解析分析师信号数据字符串时出错:", e);
                analyst_signals = {};
            }
        }
        
        return {
            time: execution.execution_time,
            decisions: decisions,
            analyst_signals: analyst_signals
        };
    });
    console.log("执行历史数据:", executionHistoryData);
} catch (e) {
    console.error("解析执行历史数据时出错:", e);
    console.error("错误详情:", e.message);
    console.error("原始数据:", executionsJsonData);
    executionHistoryData = [];
}

const tickers = Object.keys(stockPricesData);

// 创建股票选择按钮
const stockSelector = document.getElementById('stockSelector');
let activeStockChart = null;
let currentTicker = tickers.length > 0 ? tickers[0] : null;

// 只有在有股票数据时才创建按钮和图表
if (tickers.length > 0) {
    // 为每只股票创建选择按钮
    tickers.forEach((ticker, index) => {
        const btn = document.createElement('button');
        btn.className = `btn ${index === 0 ? 'btn-primary' : 'btn-outline-primary'}`;
        btn.textContent = ticker;
        btn.onclick = () => {
            // 更新按钮样式
            document.querySelectorAll('#stockSelector button').forEach(b => {
                b.className = 'btn btn-outline-primary';
            });
            btn.className = 'btn btn-primary';
            
            // 更新图表
            currentTicker = ticker;
            updateStockPriceChart();
        };
        stockSelector.appendChild(btn);
    });
    
    // 初始化第一只股票的图表
    updateStockPriceChart();
} else {
    // 如果没有股票数据，显示提示信息
    const chartContainer = document.querySelector('.stock-chart-container');
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="no-data-message"><i class="bi bi-exclamation-circle fs-1"></i><p class="mt-3">没有可用的股票价格数据</p></div>';
    }
}

// 计算时间范围的公共函数
function calculateTimeRange(timeFilter) {
    // 定义不同视图的时间配置
    const timeConfig = {
        'hour': { maxHours: 4, minHours: 1 },
        'today': { maxHours: 24, minHours: 4 },
        'all': { maxHours: 168, minHours: 24 } // 7天 = 168小时，最小1天
    };

    const PADDING_MINUTES = 10;
    const paddingMs = PADDING_MINUTES * 60 * 1000;

    // 如果没有执行历史，直接返回默认时间范围
    if (executionHistoryData.length === 0) {
        const endTime = new Date();
        const config = timeConfig[timeFilter];
        const startTime = new Date(endTime - config.maxHours * 60 * 60 * 1000);
        return {
            startTime: new Date(startTime.getTime() - paddingMs),
            endTime: new Date(endTime.getTime() + paddingMs)
        };
    }

    // 使用最后一次执行的时间作为结束时间
    const endTime = new Date(executionHistoryData[0].time);
    const config = timeConfig[timeFilter];
    const maxRangeStart = new Date(endTime - config.maxHours * 60 * 60 * 1000);
    
    // 在maxHours时间范围内找第一次执行
    const executionsInRange = executionHistoryData.filter(execution => 
        new Date(execution.time) >= maxRangeStart
    );
    
    let startTime;
    if (executionsInRange.length > 0) {
        // 取范围内最早的执行时间
        const firstExecution = new Date(executionsInRange[executionsInRange.length - 1].time);
        
        // 确保至少显示minHours的时间范围
        startTime = new Date(Math.min(
            firstExecution.getTime(),
            endTime.getTime() - (config.minHours * 60 * 60 * 1000)
        ));
    } else {
        // 如果范围内没有执行记录，使用maxRangeStart
        startTime = maxRangeStart;
    }
    
    // 添加前后padding时间
    return {
        startTime: new Date(startTime.getTime() - paddingMs),
        endTime: new Date(endTime.getTime() + paddingMs)
    };
}

function updateStockPriceChart() {
    if (!currentTicker || !stockPricesData[currentTicker] || stockPricesData[currentTicker].length === 0) {
        console.log("没有股票价格数据可显示");
        const chartContainer = document.querySelector('.stock-chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = '<div class="no-data-message"><i class="bi bi-exclamation-circle fs-1"></i><p class="mt-3">没有 ' + currentTicker + ' 的价格数据</p></div>';
        }
        return;
    }
    
    console.log(`更新${currentTicker}的价格图表`);
    
    const stockData = stockPricesData[currentTicker];
    const dates = stockData.map(item => item.date);
    const prices = stockData.map(item => item.price);
    
    if (activeStockChart) {
        activeStockChart.destroy();
    }
    
    // 确保图表容器中有canvas元素
    const chartContainer = document.querySelector('.stock-chart-container');
    if (chartContainer.querySelector('canvas') === null) {
        chartContainer.innerHTML = '<canvas id="stockPriceChart"></canvas>';
    }
    
    const stockCtx = document.getElementById('stockPriceChart').getContext('2d');
    
    // 准备标注点数据
    const annotations = [];
    const tooltipMap = new Map();
    
    // 获取时间范围
    const { startTime, endTime } = calculateTimeRange(currentTimeFilter);
    
    // 筛选在时间范围内的数据点
    const filteredIndices = [];
    const dateObjects = dates.map(d => new Date(d));
    
    dateObjects.forEach((date, index) => {
        if (date >= startTime && date <= endTime) {
            filteredIndices.push(index);
        }
    });
    
    const filteredDates = filteredIndices.map(i => dates[i]);
    const filteredPrices = filteredIndices.map(i => prices[i]);
    
    // 只在非"全部"视图下添加操作标注
    if (currentTimeFilter !== 'all') {
        executionHistoryData.forEach(execution => {
            if (execution.decisions && execution.decisions[currentTicker]) {
                const decision = execution.decisions[currentTicker];
                const executionTime = execution.time;
                const pricePoint = findClosestPricePoint(currentTicker, executionTime);
                
                if (pricePoint) {
                    // 根据操作类型设置不同的标注样式
                    let backgroundColor, borderColor, label;
                    
                    if (decision.action === 'buy') {
                        backgroundColor = 'rgba(40, 167, 69, 0.7)';
                        borderColor = 'rgba(40, 167, 69, 1)';
                        label = '买入';
                    } else if (decision.action === 'sell') {
                        backgroundColor = 'rgba(220, 53, 69, 0.7)';
                        borderColor = 'rgba(220, 53, 69, 1)';
                        label = '卖出';
                    } else if (decision.action === 'short') {
                        backgroundColor = 'rgba(255, 193, 7, 0.7)';
                        borderColor = 'rgba(255, 193, 7, 1)';
                        label = '做空';
                    } else if (decision.action === 'cover') {
                        backgroundColor = 'rgba(40, 167, 69, 0.7)';
                        borderColor = 'rgba(40, 167, 69, 1)';
                        label = '回补';
                    } else {
                        backgroundColor = 'rgba(108, 117, 125, 0.7)';
                        borderColor = 'rgba(108, 117, 125, 1)';
                        label = '持有';
                    }

                    // 构建分析师信号提示信息
                    let analystSignalsHtml = '';
                    if (execution.analyst_signals) {
                        const analyst_names = analystNamesData;
                        Object.entries(execution.analyst_signals).forEach(([analyst, signals]) => {
                            if (signals[currentTicker]) {
                                const signal = signals[currentTicker];
                                let signalText = '';
                                let signalClass = '';
                                
                                if (signal.signal === 'bullish') {
                                    signalText = '看涨';
                                    signalClass = 'signal-bullish';
                                } else if (signal.signal === 'bearish') {
                                    signalText = '看跌';
                                    signalClass = 'signal-bearish';
                                } else {
                                    signalText = '中性';
                                    signalClass = 'signal-neutral';
                                }
                                
                                const confidence = signal.confidence ? `(${signal.confidence}%)` : '';
                                analystSignalsHtml += `<div><strong>${analyst_names[analyst] || analyst}</strong>: <span class="${signalClass}">${signalText}</span>${confidence}</div>`;
                            }
                        });
                    }
                    // 在存储数据时转换为UTC时间并格式化
                    const pricePointDate = new Date(pricePoint.date);
                    const standardizedDate = new Date(pricePointDate.getTime() - (pricePointDate.getTimezoneOffset() * 60000))
                        .toISOString()
                        .replace('T', ' ')
                        .slice(0, 19);

                    // 存储自定义提示信息
                    tooltipMap.set(standardizedDate, {
                        action: label,
                        analystSignalsHtml: analystSignalsHtml,
                        price: pricePoint.price
                    });

                    // 添加标注
                    annotations.push({
                        type: 'point',
                        xValue: pricePoint.date,
                        yValue: pricePoint.price,
                        backgroundColor: backgroundColor,
                        borderColor: borderColor,
                        borderWidth: 1,
                        radius: 4,
                        label: {
                            content: label,
                            enabled: true,
                            position: 'top',
                            yAdjust: -10,
                            backgroundColor: borderColor,
                            color: 'white',
                            padding: {
                                x: 6,
                                y: 4
                            },
                            borderRadius: 4,
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        }
                    });
                }
            }
        });
    }
    
    // 创建自定义 tooltip HTML 元素
    let tooltipEl = document.getElementById('chartjs-tooltip');
    if (!tooltipEl) {
        tooltipEl = document.createElement('div');
        tooltipEl.id = 'chartjs-tooltip';
        tooltipEl.innerHTML = '<table></table>';
        document.body.appendChild(tooltipEl);
    }

    // 创建图表
    activeStockChart = new Chart(stockCtx, {
        type: 'line',
        data: {
            labels: filteredDates,
            datasets: [{
                label: `${currentTicker} 价格`,
                data: filteredPrices,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderWidth: 1,
                pointRadius: 1,
                pointHoverRadius: 4,
                tension: 0.1,
                spanGaps: false,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false,
                    text: `${currentTicker} 价格走势`,
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    enabled: false,
                    position: 'nearest',
                    external: function(context) {
                        // 如果没有 tooltip 或 tooltip 隐藏，直接返回
                        if (!context.tooltip || !context.tooltip.opacity) {
                            tooltipEl.style.display = 'none';
                            return;
                        }

                        const position = context.chart.canvas.getBoundingClientRect();
                        tooltipEl.style.display = 'block';
                        tooltipEl.style.position = 'absolute';
                        tooltipEl.style.left = position.left + window.pageXOffset + context.tooltip.caretX + 'px';
                        tooltipEl.style.top = position.top + window.pageYOffset + context.tooltip.caretY + 'px';
                        tooltipEl.style.transform = 'translate(-50%, -100%)';
                        tooltipEl.style.pointerEvents = 'none';
                        tooltipEl.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                        tooltipEl.style.boxShadow = '0 2px 5px rgba(0,0,0,0.25)';
                        tooltipEl.style.padding = '0px';
                        tooltipEl.style.borderRadius = '4px';
                        tooltipEl.style.fontSize = '12px';
                        tooltipEl.style.zIndex = '9999';

                        // 尝试将当前标签转换为相同格式
                        const labelDate = parseCustomDateFormat(context.tooltip.dataPoints[0].label);

                        // 使用标准化后的格式获取数据
                        const tooltipData = tooltipMap.get(labelDate);

                        let innerHtml = '<table>';

                        // 添加操作时间
                        innerHtml += `<tr><td>时间: ${labelDate}</td></tr>`;

                        // 添加价格信息
                        innerHtml += `<tr><td>价格: $${context.tooltip.dataPoints[0].raw.toFixed(2)}</td></tr>`;

                        if (tooltipData) {
                            // 如果有操作信息，添加操作信息
                            innerHtml += `<tr><td>操作: ${tooltipData.action}</td></tr>`;
                            if (tooltipData.analystSignalsHtml) {
                                innerHtml += `<tr><td class="analyst-signals">${tooltipData.analystSignalsHtml}</td></tr>`;
                            }
                        }
                        
                        innerHtml += '</table>';
                        tooltipEl.querySelector('table').innerHTML = innerHtml;
                    },
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                annotation: {
                    annotations: annotations
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'minute',
                        displayFormats: {
                            minute: 'HH:mm',
                            hour: 'MM-dd HH:mm'
                        }
                    },
                    min: startTime,
                    max: endTime,
                    ticks: {
                        source: 'auto',
                        autoSkip: true,
                        maxTicksLimit: 10
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '价格 ($)'
                    },
                    min: filteredPrices.length > 0 ? Math.min(...filteredPrices) * 0.995 : undefined,
                    max: filteredPrices.length > 0 ? Math.max(...filteredPrices) * 1.005 : undefined
                }
            },
            interaction: {
                intersect: false,
                mode: 'nearest'
            }
        }
    });
}
        
// 添加执行记录筛选函数
function updateExecutionHistory() {
    // 获取时间范围
    const { startTime, endTime } = calculateTimeRange(currentTimeFilter);
    
    // 获取所有执行记录行
    const executionRows = document.querySelectorAll('.execution-history table tbody tr');
    let hasVisibleRows = false;
    
    // 遍历所有行，根据时间筛选显示或隐藏
    executionRows.forEach(row => {
        const timestampElement = row.querySelector('.timestamp');
        if (timestampElement) {
            const executionTime = new Date(timestampElement.textContent);
            
            // 检查执行时间是否在筛选范围内
            if (executionTime >= startTime && executionTime <= endTime) {
                row.style.display = ''; // 显示行
                hasVisibleRows = true;
            } else {
                row.style.display = 'none'; // 隐藏行
            }
        }
    });
    
    // 更新表格容器的显示
    const tableContainer = document.querySelector('.execution-history .table-responsive');
    const noDataMessage = document.querySelector('.execution-history .text-center');
    
    if (tableContainer && noDataMessage) {
        if (hasVisibleRows) {
            // 有可见的行，显示表格
            tableContainer.style.display = '';
            noDataMessage.style.display = 'none';
        } else {
            // 没有可见的行，显示无数据消息
            tableContainer.style.display = 'none';
            noDataMessage.style.display = 'block';
            // 更新无数据消息
            const messageElement = noDataMessage.querySelector('p');
            if (messageElement) {
                messageElement.textContent = '当前筛选条件下暂无执行记录';
            }
        }
    }
}
        
// 任务操作函数
function pauseTask(taskId) {
    if (confirm('确定要暂停此任务吗？')) {
        fetch(`/api/tasks/${taskId}/pause`, {
            method: 'POST',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('暂停任务失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}
        
function resumeTask(taskId) {
    if (confirm('确定要恢复此任务吗？')) {
        fetch(`/api/tasks/${taskId}/resume`, {
            method: 'POST',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('恢复任务失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}
        
function deleteTask(taskId) {
    if (confirm('确定要删除此任务吗？此操作不可撤销！')) {
        fetch(`/api/tasks/${taskId}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/task_queue';
            } else {
                alert('删除任务失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}
      
function parseCustomDateFormat(dateStr) {
    try {
        // 处理格式如 "Apr 26, 2025, 3:58:00 a.m."
        const months = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        // 解析日期字符串
        const parts = dateStr.match(/(\w+) (\d+), (\d+), (\d+):(\d+):(\d+) ([ap])\.m\./i);
        if (!parts) {
            console.error('Failed to parse date string:', dateStr);
            return null;
        }

        const [, month, day, year, hours, minutes, seconds, ampm] = parts;
        
        // 转换小时为24小时制
        let hour = parseInt(hours);
        if (ampm.toLowerCase() === 'p' && hour < 12) hour += 12;
        if (ampm.toLowerCase() === 'a' && hour === 12) hour = 0;
        
        // 格式化为 YYYY-MM-DD HH:mm:ss
        const formattedDate = `${year}-${months[month]}-${day.padStart(2, '0')} ${
            hour.toString().padStart(2, '0')}:${
            minutes.padStart(2, '0')}:${
            seconds.padStart(2, '0')}`;

        return formattedDate;
    } catch (error) {
        console.error('Date parsing error:', error, 'for date:', dateStr);
        return null;
    }
}

// 自动刷新页面（每60秒）
setTimeout(function() {
    window.location.reload();
}, 60000);
