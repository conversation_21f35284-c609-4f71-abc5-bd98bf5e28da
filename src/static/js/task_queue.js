// 自动刷新功能
function autoRefresh() {
    console.log('开始检查任务状态...'); 
    fetch('/api/tasks/has_running')
        .then(response => response.json())
        .then(data => {
            console.log('任务状态:', data); 
            if (data.has_running) {
                console.log('存在运行中的任务，10秒后重新检查');
                setTimeout(() => {
                    fetch('/api/tasks/check_completion')
                        .then(response => response.json())
                        .then(data => {
                            console.log('完成状态:', data); 
                            if (data.has_completion) {
                                console.log('检测到任务完成，刷新页面'); 
                                location.reload();
                            } else {
                                autoRefresh();
                            }
                        })
                        .catch(error => {
                            console.error('检查任务完成状态失败:', error);
                            autoRefresh();
                        });
                }, 10000);
            } else {
                console.log('无运行中的任务'); 
                // 30秒后再次检查是否有新任务
                setTimeout(autoRefresh, 30000);
            }
        })
        .catch(error => {
            console.error('检查任务状态失败:', error);
            // 发生错误时，30秒后重试
            setTimeout(autoRefresh, 30000);
        });
}

// 确保页面加载完成后立即启动自动刷新
window.addEventListener('load', function() {
    console.log('页面加载完成，启动自动刷新');  // 添加日志
    autoRefresh();
});

// 原有的刷新按钮代码
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            location.reload();
        });
    }
    
    // 暂停任务
    document.querySelectorAll('.pause-task-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            if (confirm('确定要暂停此任务吗？')) {
                fetch(`/api/tasks/${taskId}/pause`, {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        });
    });
    
    // 恢复任务
    document.querySelectorAll('.resume-task-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            fetch(`/api/tasks/${taskId}/resume`, {
                method: 'POST',
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        });
    });
    
    // 删除任务
    document.querySelectorAll('.delete-task-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            if (confirm('确定要删除此任务吗？此操作不可撤销。')) {
                fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE',
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                });
            }
        });
    });
    
    // 查看任务详情
    const taskDetailModal = document.getElementById('taskDetailModal');
    if (taskDetailModal) {
        const taskDetailModalInstance = new bootstrap.Modal(taskDetailModal);
        
        document.querySelectorAll('.view-task-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');
                const detailContent = document.getElementById('task-detail-content');
                
                // 显示加载中
                detailContent.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                `;
                
                taskDetailModalInstance.show();
                
                // 获取任务详情
                fetch(`/api/tasks/${taskId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const task = data.task;
                            let endCondition = '永不结束';
                            if (task.end_condition === 'count') {
                                endCondition = `执行 ${task.end_count} 次后结束`;
                            } else if (task.end_condition === 'date') {
                                endCondition = `在 ${task.end_date} 后结束`;
                            }
                            
                            detailContent.innerHTML = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>任务名称:</strong> ${task.name || '未命名任务'}</p>
                                        <p><strong>股票代码:</strong> ${task.tickers}</p>
                                        <p><strong>执行频率:</strong> ${task.execution_interval}</p>
                                        <p><strong>结束条件:</strong> ${endCondition}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>创建时间:</strong> ${task.created_at}</p>
                                        <p><strong>下次执行:</strong> ${task.next_run}</p>
                                        <p><strong>已执行次数:</strong> ${task.execution_count}</p>
                                        <p><strong>状态:</strong> 
                                            ${task.status === 'active' ? '<span class="badge bg-success">运行中</span>' : 
                                              task.status === 'paused' ? '<span class="badge bg-warning">已暂停</span>' : 
                                              task.status === 'completed' ? '<span class="badge bg-primary">已完成</span>' : 
                                              '<span class="badge bg-danger">错误</span>'}
                                        </p>
                                    </div>
                                </div>
                                <hr>
                                <h6>任务配置</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>初始现金:</strong> ${task.initial_cash}</p>
                                        <p><strong>保证金要求:</strong> ${task.margin_requirement}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>分析师:</strong> ${task.analysts.join(', ')}</p>
                                        <p><strong>模型:</strong> ${task.model}</p>
                                        <p><strong>显示推理过程:</strong> ${task.show_reasoning ? '是' : '否'}</p>
                                    </div>
                                </div>
                            `;
                        } else {
                            detailContent.innerHTML = `<div class="alert alert-danger">加载任务详情失败: ${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        detailContent.innerHTML = `<div class="alert alert-danger">加载任务详情失败，请重试</div>`;
                    });
            });
        });
    }
    
    // 初始化所有popover
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true,
            placement: 'auto'
        })
    })
    
    // 点击其他地方关闭所有popover
    document.addEventListener('click', function(e) {
        if (!e.target.matches('[data-bs-toggle="popover"]')) {
            popoverList.forEach(function(popover) {
                popover.hide();
            });
        }
    });
});