document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            html: true
        })
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true
        })
    });
    
    // 获取所有标签页按钮
    var tabButtons = document.querySelectorAll('#historyTabs button[data-bs-toggle="tab"]');

    // 从本地存储中获取上次选择的标签页
    var activeTabId = localStorage.getItem('activeHistoryTab');

    // 如果有存储的标签页ID，则激活该标签页；否则激活默认标签页
    if (activeTabId) {
        var tabToActivate = document.querySelector(activeTabId);
        if (tabToActivate) {
            var tab = new bootstrap.Tab(tabToActivate);
            tab.show();
        }
    } else {
        // 默认激活回测标签页
        var defaultTab = new bootstrap.Tab(document.querySelector('#backtest-tab'));
        defaultTab.show();
        localStorage.setItem('activeHistoryTab', '#backtest-tab');
    }
    
    // 为每个标签页按钮添加点击事件，保存选择到本地存储
    tabButtons.forEach(function(button) {
        button.addEventListener('click', function(event) {
            localStorage.setItem('activeHistoryTab', '#' + event.target.id);
        });
    });
    
    // 分页配置
    const itemsPerPage = 20; // 每页显示的记录数
    let backtestCurrentPage = 1;
    let tradingCurrentPage = 1;
    
    // 从本地存储中获取上次的页码
    const savedBacktestPage = localStorage.getItem('backtestCurrentPage');
    if (savedBacktestPage) {
        backtestCurrentPage = parseInt(savedBacktestPage);
    }
    
    const savedTradingPage = localStorage.getItem('tradingCurrentPage');
    if (savedTradingPage) {
        tradingCurrentPage = parseInt(savedTradingPage);
    }
    
    // 股票筛选功能 - 回测记录
    const backtestStockFilter = document.getElementById('backtest-stock-filter');
    const backtestFilterReset = document.getElementById('backtest-filter-reset');
    const backtestTable = document.getElementById('backtest-records-table');
    const backtestPagination = document.getElementById('backtest-pagination');
    
    if (backtestStockFilter && backtestTable) {
        // 从本地存储中获取上次选择的股票
        const savedBacktestTicker = localStorage.getItem('backtestSelectedTicker');
        if (savedBacktestTicker) {
            backtestStockFilter.value = savedBacktestTicker;
        }
        
        // 筛选函数
        function filterBacktestRecords() {
            const selectedTicker = backtestStockFilter.value;
            // 保存选择到本地存储
            localStorage.setItem('backtestSelectedTicker', selectedTicker);
            
            const rows = backtestTable.querySelectorAll('tbody tr');
            let visibleRows = [];
            
            // 先筛选符合条件的行
            rows.forEach(row => {
                const tickers = row.getAttribute('data-tickers').split(',');
                if (selectedTicker === 'all' || tickers.includes(selectedTicker)) {
                    row.classList.add('filtered-in');
                    visibleRows.push(row);
                } else {
                    row.classList.remove('filtered-in');
                    row.style.display = 'none';
                }
            });
            
            // 重置到第一页
            backtestCurrentPage = 1;
            localStorage.setItem('backtestCurrentPage', backtestCurrentPage);
            
            // 更新分页
            updateBacktestPagination(visibleRows.length);
            
            // 显示当前页的记录
            showBacktestPage(backtestCurrentPage);
        }
        
        // 更新分页控件
        function updateBacktestPagination(totalItems) {
            if (!backtestPagination) return;
            
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            backtestPagination.innerHTML = '';
            
            if (totalPages <= 1) return;
            
            // 上一页按钮
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${backtestCurrentPage === 1 ? 'disabled' : ''}`;
            const prevLink = document.createElement('a');
            prevLink.className = 'page-link';
            prevLink.href = '#';
            prevLink.textContent = '上一页';
            prevLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (backtestCurrentPage > 1) {
                    backtestCurrentPage--;
                    localStorage.setItem('backtestCurrentPage', backtestCurrentPage);
                    showBacktestPage(backtestCurrentPage);
                    updateBacktestPagination(totalItems);
                }
            });
            prevLi.appendChild(prevLink);
            backtestPagination.appendChild(prevLi);
            
            // 页码按钮
            let startPage = Math.max(1, backtestCurrentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === backtestCurrentPage ? 'active' : ''}`;
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    backtestCurrentPage = i;
                    localStorage.setItem('backtestCurrentPage', backtestCurrentPage);
                    showBacktestPage(backtestCurrentPage);
                    updateBacktestPagination(totalItems);
                });
                pageLi.appendChild(pageLink);
                backtestPagination.appendChild(pageLi);
            }
            
            // 下一页按钮
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${backtestCurrentPage === totalPages ? 'disabled' : ''}`;
            const nextLink = document.createElement('a');
            nextLink.className = 'page-link';
            nextLink.href = '#';
            nextLink.textContent = '下一页';
            nextLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (backtestCurrentPage < totalPages) {
                    backtestCurrentPage++;
                    localStorage.setItem('backtestCurrentPage', backtestCurrentPage);
                    showBacktestPage(backtestCurrentPage);
                    updateBacktestPagination(totalItems);
                }
            });
            nextLi.appendChild(nextLink);
            backtestPagination.appendChild(nextLi);
            
            // 添加总页数信息
            const infoLi = document.createElement('li');
            infoLi.className = 'page-item disabled';
            const infoSpan = document.createElement('span');
            infoSpan.className = 'page-link';
            infoSpan.textContent = `共 ${totalPages} 页`;
            infoLi.appendChild(infoSpan);
            backtestPagination.appendChild(infoLi);
        }
        
        // 显示指定页的记录
        function showBacktestPage(page) {
            const rows = backtestTable.querySelectorAll('tbody tr.filtered-in');
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            Array.from(rows).forEach((row, index) => {
                if (index >= startIndex && index < endIndex) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 添加事件监听器
        backtestStockFilter.addEventListener('change', filterBacktestRecords);
        
        // 重置按钮
        if (backtestFilterReset) {
            backtestFilterReset.addEventListener('click', function() {
                backtestStockFilter.value = 'all';
                localStorage.setItem('backtestSelectedTicker', 'all');
                filterBacktestRecords();
            });
        }
        
        // 初始应用筛选
        filterBacktestRecords();
    }
    
    // 股票筛选功能 - 交易记录
    const tradingStockFilter = document.getElementById('trading-stock-filter');
    const tradingFilterReset = document.getElementById('trading-filter-reset');
    const tradingTable = document.getElementById('trading-records-table');
    const tradingPagination = document.getElementById('trading-pagination');
    
    if (tradingStockFilter && tradingTable) {
        // 从本地存储中获取上次选择的股票
        const savedTradingTicker = localStorage.getItem('tradingSelectedTicker');
        if (savedTradingTicker) {
            tradingStockFilter.value = savedTradingTicker;
        }
        
        // 筛选函数
        function filterTradingRecords() {
            const selectedTicker = tradingStockFilter.value;
            // 保存选择到本地存储
            localStorage.setItem('tradingSelectedTicker', selectedTicker);
            
            const rows = tradingTable.querySelectorAll('tbody tr');
            let visibleRows = [];
            
            // 先筛选符合条件的行
            rows.forEach(row => {
                const tickers = row.getAttribute('data-tickers').split(',');
                if (selectedTicker === 'all' || tickers.includes(selectedTicker)) {
                    row.classList.add('filtered-in');
                    visibleRows.push(row);
                } else {
                    row.classList.remove('filtered-in');
                    row.style.display = 'none';
                }
            });
            
            // 重置到第一页
            tradingCurrentPage = 1;
            localStorage.setItem('tradingCurrentPage', tradingCurrentPage);
            
            // 更新分页
            updateTradingPagination(visibleRows.length);
            
            // 显示当前页的记录
            showTradingPage(tradingCurrentPage);
        }
        
        // 更新分页控件
        function updateTradingPagination(totalItems) {
            if (!tradingPagination) return;
            
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            tradingPagination.innerHTML = '';
            
            if (totalPages <= 1) return;
            
            // 上一页按钮
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${tradingCurrentPage === 1 ? 'disabled' : ''}`;
            const prevLink = document.createElement('a');
            prevLink.className = 'page-link';
            prevLink.href = '#';
            prevLink.textContent = '上一页';
            prevLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (tradingCurrentPage > 1) {
                    tradingCurrentPage--;
                    localStorage.setItem('tradingCurrentPage', tradingCurrentPage);
                    showTradingPage(tradingCurrentPage);
                    updateTradingPagination(totalItems);
                }
            });
            prevLi.appendChild(prevLink);
            tradingPagination.appendChild(prevLi);
            
            // 页码按钮
            let startPage = Math.max(1, tradingCurrentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === tradingCurrentPage ? 'active' : ''}`;
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    tradingCurrentPage = i;
                    localStorage.setItem('tradingCurrentPage', tradingCurrentPage);
                    showTradingPage(tradingCurrentPage);
                    updateTradingPagination(totalItems);
                });
                pageLi.appendChild(pageLink);
                tradingPagination.appendChild(pageLi);
            }
            
            // 下一页按钮
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${tradingCurrentPage === totalPages ? 'disabled' : ''}`;
            const nextLink = document.createElement('a');
            nextLink.className = 'page-link';
            nextLink.href = '#';
            nextLink.textContent = '下一页';
            nextLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (tradingCurrentPage < totalPages) {
                    tradingCurrentPage++;
                    localStorage.setItem('tradingCurrentPage', tradingCurrentPage);
                    showTradingPage(tradingCurrentPage);
                    updateTradingPagination(totalItems);
                }
            });
            nextLi.appendChild(nextLink);
            tradingPagination.appendChild(nextLi);
            
            // 添加总页数信息
            const infoLi = document.createElement('li');
            infoLi.className = 'page-item disabled';
            const infoSpan = document.createElement('span');
            infoSpan.className = 'page-link';
            infoSpan.textContent = `共 ${totalPages} 页`;
            infoLi.appendChild(infoSpan);
            tradingPagination.appendChild(infoLi);
        }
        
        // 显示指定页的记录
        function showTradingPage(page) {
            const rows = tradingTable.querySelectorAll('tbody tr.filtered-in');
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            Array.from(rows).forEach((row, index) => {
                if (index >= startIndex && index < endIndex) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 添加事件监听器
        tradingStockFilter.addEventListener('change', filterTradingRecords);
        
        // 重置按钮
        if (tradingFilterReset) {
            tradingFilterReset.addEventListener('click', function() {
                tradingStockFilter.value = 'all';
                localStorage.setItem('tradingSelectedTicker', 'all');
                filterTradingRecords();
            });
        }
        
        // 初始应用筛选
        filterTradingRecords();
    }
    
    // 点击其他地方关闭所有popover
    document.addEventListener('click', function(e) {
        if (!e.target.matches('[data-bs-toggle="popover"]')) {
            popoverList.forEach(function(popover) {
                popover.hide();
            });
        }
    });
});
