// 图表配置常量
const CHART_CONFIG = {
    colors: {
        buy: 'rgb(40, 167, 69)',    // 绿色
        sell: 'rgb(220, 53, 69)',   // 红色
        short: 'rgb(255, 193, 7)',  // 黄色
        cover: 'rgb(40, 167, 69)',  // 绿色
        hold: 'rgb(108, 117, 125)', // 灰色
        line: 'rgb(75, 192, 192)'   // 主线条颜色
    },
    defaultHeight: 400,
    priceBuffer: 0.05, // 价格范围缓冲区(5%)
};

// 交易动作映射
const TRADE_ACTIONS = {
    BUY: { color: CHART_CONFIG.colors.buy, label: '买入' },
    SELL: { color: CHART_CONFIG.colors.sell, label: '卖出' },
    SHORT: { color: CHART_CONFIG.colors.short, label: '做空' },
    COVER: { color: CHART_CONFIG.colors.cover, label: '回补' },
    HOLD: { color: CHART_CONFIG.colors.hold, label: '持有' }
};

class BacktestChart {
    constructor() {
        this.portfolioChart = null;
        this.stockChart = null;
        this.currentTicker = null;
        this.stockPricesData = {};
        this.tradeHistoryData = [];
        this.dailyDecisionsData = {};
        this.dailyAnalystSignalsData = {};
        this.tooltipEl = null;
        
        // 获取股票列表
        this.tickers = Object.keys(this.stockPricesData);
        console.log('股票列表:', this.tickers);
        
        if (this.tickers.length > 0) {
            this.currentTicker = this.tickers[0];
            console.log('当前选中股票:', this.currentTicker);
        } else {
            this.currentTicker = null;
            console.warn('没有可用的股票数据');
        }
        
        try {
            this.initTooltips();
            this.initData();
            this.createTooltipElement(); // 确保在创建图表前先创建tooltip元素
            this.createPortfolioChart();
            this.initStockSelector();
            this.createDecisionAnalystTable();
            // 添加交易历史过滤功能
            this.initTradeHistoryFilter();
        } catch (error) {
            console.error('BacktestChart初始化错误:', error);
            this.handleError('初始化失败', error);
        }
    }

    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        this.tooltipList = tooltipTriggerList.map(trigger => 
            new bootstrap.Tooltip(trigger, { html: true })
        );
    }

    initData() {
        try {
            // 初始化投资组合数据
            if (typeof portfolioValuesData !== 'undefined' && portfolioValuesData) {
                // 尝试解析JSON字符串，如果已经是对象则直接使用
                if (typeof portfolioValuesData === 'string') {
                    try {
                        this.portfolioData = JSON.parse(portfolioValuesData);
                    } catch (e) {
                        console.error('解析投资组合数据失败:', e);
                        this.portfolioData = [];
                    }
                } else {
                    this.portfolioData = portfolioValuesData;
                }
            } else {
                this.portfolioData = [];
                console.warn('没有投资组合数据');
            }
            
            // 初始化分析师名称数据
            if (typeof analystNamesData !== 'undefined' && analystNamesData) {
                if (typeof analystNamesData === 'string') {
                    try {
                        this.analystNames = JSON.parse(analystNamesData);
                    } catch (e) {
                        console.error('解析分析师名称数据失败:', e);
                        this.analystNames = {};
                    }
                } else {
                    this.analystNames = analystNamesData;
                }
            } else {
                this.analystNames = {};
                console.warn('没有分析师名称数据');
            }
            
            // 初始化股票价格数据
            if (typeof stockPricesData !== 'undefined' && stockPricesData) {
                // 尝试解析JSON字符串，如果已经是对象则直接使用
                if (typeof stockPricesData === 'string') {
                    try {
                        this.stockPricesData = JSON.parse(stockPricesData);
                    } catch (e) {
                        console.error('解析股票价格数据失败:', e);
                        this.stockPricesData = {};
                    }
                } else {
                    this.stockPricesData = stockPricesData;
                }
                
                // 打印股票价格数据，用于调试
                console.log('股票价格数据:', this.stockPricesData);
            } else {
                this.stockPricesData = {};
                console.warn('没有股票价格数据');
            }
            
            // 初始化交易历史数据
            if (typeof tradeHistoryData !== 'undefined' && tradeHistoryData) {
                if (typeof tradeHistoryData === 'string') {
                    try {
                        this.tradeHistoryData = JSON.parse(tradeHistoryData);
                    } catch (e) {
                        console.error('解析交易历史数据失败:', e);
                        this.tradeHistoryData = [];
                    }
                } else {
                    this.tradeHistoryData = tradeHistoryData;
                }
            } else {
                this.tradeHistoryData = [];
                console.warn('没有交易历史数据');
            }
            
            // 初始化每日决策数据
            if (typeof dailyDecisionsData !== 'undefined' && dailyDecisionsData) {
                // 尝试解析JSON字符串，如果已经是对象则直接使用
                if (typeof dailyDecisionsData === 'string') {
                    try {
                        this.dailyDecisionsData = JSON.parse(dailyDecisionsData);
                    } catch (e) {
                        console.error('解析每日决策数据失败:', e);
                        this.dailyDecisionsData = {};
                    }
                } else {
                    this.dailyDecisionsData = dailyDecisionsData;
                }
            } else {
                this.dailyDecisionsData = {};
                console.warn('没有每日决策数据');
            }
            
            // 初始化每日分析师信号数据
            if (typeof dailyAnalystSignalsData !== 'undefined' && dailyAnalystSignalsData) {
                // 尝试解析JSON字符串，如果已经是对象则直接使用
                if (typeof dailyAnalystSignalsData === 'string') {
                    try {
                        this.dailyAnalystSignalsData = JSON.parse(dailyAnalystSignalsData);
                    } catch (e) {
                        console.error('解析每日分析师信号数据失败:', e);
                        this.dailyAnalystSignalsData = {};
                    }
                } else {
                    this.dailyAnalystSignalsData = dailyAnalystSignalsData;
                }
            } else {
                this.dailyAnalystSignalsData = {};
                console.warn('没有每日分析师信号数据');
            }
            
            // 初始化性能指标数据
            if (typeof metricsData !== 'undefined' && metricsData) {
                if (typeof metricsData === 'string') {
                    try {
                        this.metrics = JSON.parse(metricsData);
                    } catch (e) {
                        console.error('解析性能指标数据失败:', e);
                        this.metrics = {};
                    }
                } else {
                    this.metrics = metricsData;
                }
                
                // 确保初始资金值存在
                if (!this.metrics.initial_capital && this.portfolioData.length > 0) {
                    this.metrics.initial_capital = this.portfolioData[0].value;
                }
            } else {
                this.metrics = {};
                console.warn('没有性能指标数据');
            }
            
            // 获取股票列表
            this.tickers = Object.keys(this.stockPricesData);
            console.log('股票列表:', this.tickers);
            
            if (this.tickers.length > 0) {
                this.currentTicker = this.tickers[0];
                console.log('当前选中股票:', this.currentTicker);
            } else {
                this.currentTicker = null;
                console.warn('没有可用的股票数据');
            }
            
        } catch (error) {
            console.error('数据初始化错误:', error);
            this.handleError('数据初始化失败', error);
        }
    }

    parseJsonSafely(jsonString, defaultValue) {
        if (!jsonString || jsonString === '{}') return defaultValue;
        try {
            const parsed = JSON.parse(jsonString);
            return typeof parsed === 'object' ? parsed : defaultValue;
        } catch (error) {
            console.error('JSON解析错误:', error);
            return defaultValue;
        }
    }

    createTooltipElement() {
        // 如果已经存在，先移除
        const existingTooltip = document.getElementById('chartjs-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
        
        // 创建新的tooltip元素
        this.tooltipEl = document.createElement('div');
        this.tooltipEl.id = 'chartjs-tooltip';
        this.tooltipEl.className = 'chartjs-tooltip';
        this.tooltipEl.innerHTML = '<table></table>';
        document.body.appendChild(this.tooltipEl);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #chartjs-tooltip {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 4px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.25);
                padding: 6px;
                pointer-events: none;
                position: absolute;
                transform: translate(-50%, -100%);
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.3s;
                font-size: 12px;
            }
            
            #chartjs-tooltip table {
                margin: 0;
            }
            
            #chartjs-tooltip .analyst-signals {
                margin-top: 4px;
                padding-top: 4px;
                border-top: 1px solid #eee;
            }
            
            #chartjs-tooltip .signal-bullish {
                color: #28a745;
                font-weight: bold;
            }
            
            #chartjs-tooltip .signal-bearish {
                color: #dc3545;
                font-weight: bold;
            }
            
            #chartjs-tooltip .signal-neutral {
                color: #ffc107;
                font-weight: bold;
            }
            
            #chartjs-tooltip .text-success {
                color: #28a745;
                font-weight: bold;
            }
            
            #chartjs-tooltip .text-danger {
                color: #dc3545;
                font-weight: bold;
            }
        `;
        document.head.appendChild(style);
    }

    createPortfolioChart() {
        const ctx = document.getElementById('portfolioChart')?.getContext('2d');
        if (!ctx) return;

        const { dates, values, cumulativeReturns } = this.extractPortfolioData();
        
        // 添加自定义插件来处理线段颜色
        const colorChangePlugin = {
            id: 'colorChange',
            beforeDatasetsDraw: function(chart, args, options) {
                const meta = chart.getDatasetMeta(1); // 获取收益率数据集
                if (!meta.visible) return;
                
                const ctx = chart.ctx;
                const xScale = chart.scales.x;
                const yScale = chart.scales.y1;
                
                // 绘制零线
                ctx.save();
                ctx.beginPath();
                ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.lineWidth = 1;
                ctx.setLineDash([5, 5]);
                
                const y0 = yScale.getPixelForValue(0);
                ctx.moveTo(xScale.left, y0);
                ctx.lineTo(xScale.right, y0);
                ctx.stroke();
                ctx.restore();
            }
        };
        
        // 设置图表配置
        const chartOptions = this.getPortfolioChartOptions();
        chartOptions.maintainAspectRatio = false; // 允许图表自适应容器大小
        
        this.portfolioChart = new Chart(ctx, {
            type: 'line',
            data: this.getPortfolioChartData(dates, values, cumulativeReturns),
            options: chartOptions,
            plugins: [colorChangePlugin]
        });
        
        // 添加窗口大小变化监听器，以便在窗口调整大小时重新渲染图表
        window.addEventListener('resize', () => {
            if (this.portfolioChart) {
                this.portfolioChart.resize();
            }
        });
    }

    extractPortfolioData() {
        const dates = this.portfolioData.map(item => item.date);
        const values = this.portfolioData.map(item => item.value);
        
        // 计算每日收益率
        const returns = [];
        for (let i = 0; i < values.length; i++) {
            if (i === 0) {
                // 第一天的收益率相对于初始资金
                const initialValue = this.metrics.initial_capital || values[0];
                returns.push(((values[i] / initialValue) - 1) * 100);
            } else {
                // 其他天的收益率相对于前一天
                returns.push(((values[i] / values[i-1]) - 1) * 100);
            }
        }
        
        // 计算累计收益率
        const cumulativeReturns = [];
        const initialValue = this.metrics.initial_capital || values[0];
        for (let i = 0; i < values.length; i++) {
            cumulativeReturns.push(((values[i] / initialValue) - 1) * 100);
        }
        
        return { dates, values, returns, cumulativeReturns };
    }

    getPortfolioChartData(dates, values, cumulativeReturns) {
        // 创建点颜色数组，根据收益率正负设置不同颜色
        const pointBackgroundColors = cumulativeReturns.map(return_value => 
            return_value >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)'
        );
        
        // 创建点边框颜色数组
        const pointBorderColors = cumulativeReturns.map(return_value => 
            return_value >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)'
        );
        
        return {
            labels: dates,
            datasets: [
                {
                    label: '投资组合价值',
                    data: values,
                    borderColor: CHART_CONFIG.colors.line,
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    fill: true,
                    yAxisID: 'y',
                    pointRadius: 3,
                    pointBackgroundColor: pointBackgroundColors,
                    pointBorderColor: pointBorderColors,
                    pointHoverRadius: 5
                },
                {
                    label: '累计收益率 (%)',
                    data: cumulativeReturns,
                    borderColor: function(context) {
                        const index = context.dataIndex;
                        const value = context.dataset.data[index];
                        return value >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)';
                    },
                    backgroundColor: 'rgba(255, 99, 132, 0)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: false,
                    yAxisID: 'y1',
                    pointRadius: 3,
                    pointBackgroundColor: pointBackgroundColors,
                    pointBorderColor: pointBorderColors,
                    pointHoverRadius: 5,
                    segment: {
                        borderColor: function(context) {
                            if (!context.p0.skip && !context.p1.skip) {
                                const value0 = context.p0.parsed.y;
                                const value1 = context.p1.parsed.y;
                                
                                // 如果两点都是正值或都是负值，使用相同颜色
                                if ((value0 >= 0 && value1 >= 0) || (value0 < 0 && value1 < 0)) {
                                    return value0 >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)';
                                }
                                
                                // 如果一正一负，使用渐变色
                                return createGradient(context.p0, context.p1, 
                                    'rgba(40, 167, 69, 1)', 'rgba(220, 53, 69, 1)');
                            }
                            return 'rgba(75, 192, 192, 1)';
                        }
                    }
                }
            ]
        };
    }

    getPortfolioChartOptions() {
        const self = this;
        return {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false
            },
            plugins: {
                title: {
                    display: true,
                    text: '投资组合价值与收益率变化'
                },
                tooltip: {
                    enabled: false,
                    external: function(context) {
                        self.handlePortfolioTooltip(context);
                    }
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '价值 ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '收益率 (%)'
                    },
                    grid: {
                        drawOnChartArea: false
                    },
                    ticks: {
                        callback: function(value) {
                            const sign = value >= 0 ? '+' : '';
                            return sign + value.toFixed(2) + '%';
                        },
                        color: function(context) {
                            const value = context.tick.value;
                            return value >= 0 ? '#28a745' : '#dc3545';
                        }
                    }
                }
            }
        };
    }

    handlePortfolioTooltip(context) {
        // 如果没有 tooltip 或 tooltip 隐藏，直接返回
        if (!context.tooltip || !context.tooltip.opacity) {
            if (this.tooltipEl) this.tooltipEl.style.opacity = 0;
            return;
        }
        
        // 获取当前数据点
        const dataPoints = context.tooltip.dataPoints;
        if (!dataPoints || dataPoints.length === 0) return;
        
        // 设置 tooltip 位置
        const position = context.chart.canvas.getBoundingClientRect();
        this.tooltipEl.style.opacity = 1;
        this.tooltipEl.style.position = 'absolute';
        this.tooltipEl.style.left = position.left + window.pageXOffset + context.tooltip.caretX + 'px';
        this.tooltipEl.style.top = position.top + window.pageYOffset + context.tooltip.caretY - 10 + 'px';
        this.tooltipEl.style.transform = 'translate(-50%, -100%)';
        
        // 构建 tooltip 内容
        let innerHtml = '<table>';
        
        // 添加日期
        const date = dataPoints[0].label;
        innerHtml += `<tr><td>日期:</td><td>${this.formatDate(date)}</td></tr>`;
        
        // 添加投资组合价值
        const portfolioValue = dataPoints.find(p => p.dataset.label === '投资组合价值')?.raw;
        if (portfolioValue !== undefined) {
            innerHtml += `<tr><td>投资组合价值:</td><td>$${portfolioValue.toFixed(2)}</td></tr>`;
        }
        
        // 添加累计收益率
        const returnValue = dataPoints.find(p => p.dataset.label === '累计收益率 (%)')?.raw;
        if (returnValue !== undefined) {
            const sign = returnValue >= 0 ? '+' : '';
            const colorClass = returnValue >= 0 ? 'text-success' : 'text-danger';
            innerHtml += `<tr><td>累计收益率:</td><td class="${colorClass}">${sign}${returnValue.toFixed(2)}%</td></tr>`;
        }
        
        innerHtml += '</table>';
        this.tooltipEl.querySelector('table').innerHTML = innerHtml;
    }

    initStockSelector() {
        const stockSelector = document.getElementById('stockSelector');
        if (!stockSelector || !this.tickers.length) {
            this.handleNoStockData();
            return;
        }

        this.tickers.forEach((ticker, index) => {
            this.createStockButton(stockSelector, ticker, index === 0);
        });

        this.showStockChart(this.currentTicker);
    }

    createStockButton(container, ticker, isActive) {
        const btn = document.createElement('button');
        btn.type = 'button';
        btn.className = `btn ${isActive ? 'btn-primary' : 'btn-outline-primary'}`;
        btn.textContent = ticker;
        btn.onclick = () => {
            // 更新按钮样式
            container.querySelectorAll('button').forEach(b => {
                b.className = 'btn btn-outline-primary';
            });
            btn.className = 'btn btn-primary';
            
            // 更新当前选中的股票
            this.currentTicker = ticker;
            console.log('切换到股票:', this.currentTicker);
            
            // 更新股票价格图表
            this.showStockChart(ticker);
            
            // 更新决策和分析师信号表格
            this.updateAnalystSignalsForCurrentTicker();
            
            // 过滤交易历史，只显示当前股票
            this.filterTradeHistory(ticker);
        };
        container.appendChild(btn);
    }
    
    // 根据当前选中的股票更新分析师信号表格
    updateAnalystSignalsForCurrentTicker() {
        const dateSelector = document.getElementById('analyst-date-selector-inline');
        if (!dateSelector) return;
        
        const selectedDate = dateSelector.value;
        const contentContainer = document.getElementById('decision-analyst-content');
        if (contentContainer) {
            this.updateDecisionAnalystContent(contentContainer, selectedDate);
        }
    }

    handleStockSelection(selectedBtn, ticker) {
        document.querySelectorAll('#stockSelector button')
            .forEach(btn => btn.className = 'btn btn-outline-primary');
        selectedBtn.className = 'btn btn-primary';
        this.currentTicker = ticker;
        this.showStockChart(ticker);
    }

    showStockChart(ticker) {
        console.log(`显示 ${ticker} 的股票图表`);
        
        if (!ticker) {
            console.warn('没有指定股票代码');
            this.handleNoStockData();
            return;
        }
        
        const stockData = this.stockPricesData[ticker];
        console.log(`${ticker} 的股票数据:`, stockData);
        
        if (!this.validateStockData(stockData)) {
            console.warn(`${ticker} 的股票数据无效`);
            return;
        }

        const { dates, prices } = this.extractStockData(stockData);
        const annotations = this.createTradeAnnotations(dates, prices, ticker);
        
        this.updateStockChart(dates, prices, ticker, annotations);
    }

    validateStockData(stockData) {
        if (!stockData || !Array.isArray(stockData) || stockData.length === 0) {
            console.warn('股票数据无效或为空');
            this.handleNoStockData();
            return false;
        }
        return true;
    }

    extractStockData(stockData) {
        try {
            return {
                dates: stockData.map(item => item.date),
                prices: stockData.map(item => parseFloat(item.price))
            };
        } catch (error) {
            console.error('提取股票数据时出错:', error);
            this.handleError('提取股票数据失败', error);
            return { dates: [], prices: [] };
        }
    }

    createTradeAnnotations(dates, prices, ticker) {
        const annotations = {};
        const tickerTrades = this.tradeHistoryData.filter(trade => trade.ticker === ticker);

        tickerTrades.forEach((trade, index) => {
            const dateIndex = dates.indexOf(trade.date);
            if (dateIndex !== -1) {
                const action = trade.action.toUpperCase();
                const tradeConfig = TRADE_ACTIONS[action] || TRADE_ACTIONS.HOLD;
                
                annotations[`point-${index}`] = this.createAnnotationPoint(
                    dateIndex, 
                    prices[dateIndex], 
                    tradeConfig
                );
            }
        });

        return annotations;
    }

    createAnnotationPoint(dateIndex, price, tradeConfig) {
        return {
            type: 'point',
            xValue: dateIndex,
            yValue: price,
            backgroundColor: tradeConfig.color,
            borderColor: 'white',
            borderWidth: 2,
            radius: 6,
            label: {
                display: true,
                content: tradeConfig.label,
                position: 'top',
                backgroundColor: tradeConfig.color,
                color: 'white',
                font: { size: 10 }
            }
        };
    }

    updateStockChart(dates, prices, ticker, annotations) {
        console.log(`更新 ${ticker} 的股票图表`);
        
        if (!dates || !prices || dates.length === 0 || prices.length === 0) {
            console.warn('日期或价格数据为空');
            this.handleNoStockData();
            return;
        }
        
        if (this.stockChart) {
            console.log('销毁旧图表');
            this.stockChart.destroy();
        }

        const canvas = this.prepareChartCanvas();
        if (!canvas) {
            console.error('无法获取图表画布');
            return;
        }

        const ctx = canvas.getContext('2d');
        
        // 创建日期到交易信息的映射
        const dateToTradeMap = new Map();
        this.tradeHistoryData.forEach(trade => {
            if (trade.ticker === ticker) {
                dateToTradeMap.set(trade.date, trade);
            }
        });
        
        // 打印图表配置信息
        console.log('图表数据:', this.getStockChartData(dates, prices, ticker));
        console.log('图表选项:', this.getStockChartOptions(annotations, ticker, dateToTradeMap));
        
        try {
            this.stockChart = new Chart(ctx, {
                type: 'line',
                data: this.getStockChartData(dates, prices, ticker),
                options: this.getStockChartOptions(annotations, ticker, dateToTradeMap)
            });
            console.log('股票图表创建成功');
        } catch (error) {
            console.error('创建股票图表时出错:', error);
            this.handleError('创建股票图表失败', error);
        }
    }

    prepareChartCanvas() {
        const container = document.querySelector('.stock-chart-container');
        if (!container) {
            console.error('找不到股票图表容器');
            return null;
        }

        let canvas = container.querySelector('canvas');
        if (!canvas) {
            console.log('创建新的画布元素');
            container.innerHTML = '<canvas id="stockPriceChart"></canvas>';
            canvas = container.querySelector('canvas');
        }

        canvas.width = container.offsetWidth;
        canvas.height = CHART_CONFIG.defaultHeight;
        return canvas;
    }

    getStockChartData(dates, prices, ticker) {
        return {
            labels: dates,
            datasets: [{
                label: `${ticker} 价格`,
                data: prices,
                borderColor: CHART_CONFIG.colors.line,
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.1,
                fill: true
            }]
        };
    }

    getStockChartOptions(annotations, ticker, dateToTradeMap) {
        const self = this;
        return {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: `${ticker} 股票价格走势`
                },
                tooltip: {
                    enabled: false,
                    external: function(context) {
                        self.handleTooltip(context, ticker, dateToTradeMap);
                    }
                },
                annotation: { annotations }
            },
            scales: this.getCommonScales('价格 ($)'),
            interaction: {
                intersect: false,
                mode: 'index'
            }
        };
    }

    handleTooltip(context, ticker, dateToTradeMap) {
        // 如果没有 tooltip 或 tooltip 隐藏，直接返回
        if (!context.tooltip || !context.tooltip.opacity) {
            if (this.tooltipEl) this.tooltipEl.style.opacity = 0;
            return;
        }
        
        // 获取当前日期和价格
        const dataPoint = context.tooltip.dataPoints[0];
        const currentDate = dataPoint.label;
        const currentPrice = dataPoint.raw;
        
        // 设置 tooltip 位置
        const position = context.chart.canvas.getBoundingClientRect();
        this.tooltipEl.style.left = position.left + window.pageXOffset + context.tooltip.caretX + 'px';
        this.tooltipEl.style.top = position.top + window.pageYOffset + context.tooltip.caretY - 10 + 'px';
        this.tooltipEl.style.transform = 'translate(-50%, -100%)';
        this.tooltipEl.style.opacity = 1;
        this.tooltipEl.style.fontSize = '12px';
        
        // 获取交易信息
        const trade = dateToTradeMap.get(currentDate);
        
        // 获取分析师信号
        let analystSignalsHtml = '';
        let analystSignals = null;
        const formattedDate = this.formatDateForLookup(currentDate);
        
        if (this.dailyAnalystSignalsData && this.dailyAnalystSignalsData[formattedDate]) {
            analystSignals = this.dailyAnalystSignalsData[formattedDate];
            
            if (analystSignals && Object.keys(analystSignals).length > 0) {
                analystSignalsHtml = '<div class="analyst-signals">';
                
                Object.entries(analystSignals).forEach(([analyst, signals]) => {
                    if (signals && signals[ticker]) {
                        const signal = signals[ticker];
                        let signalClass = '';
                        let signalText = '';
                        
                        if (signal.signal === 'bullish') {
                            signalClass = 'signal-bullish';
                            signalText = '看涨';
                        } else if (signal.signal === 'bearish') {
                            signalClass = 'signal-bearish';
                            signalText = '看跌';
                        } else {
                            signalClass = 'signal-neutral';
                            signalText = '中性';
                        }
                        
                        const analystName = this.analystNames[analyst] || analyst;
                        const confidence = signal.confidence ? `(${signal.confidence}%)` : '';
                        
                        analystSignalsHtml += `<div><strong>${analystName}</strong>: <span class="${signalClass}">${signalText}</span>${confidence}</div>`;
                    }
                });
                
                analystSignalsHtml += '</div>';
            }
        }
        
        // 构建 tooltip 内容
        let innerHtml = '<table>';
        
        // 添加日期和价格信息
        innerHtml += `<tr><td>日期:</td><td>${this.formatDate(currentDate)}</td></tr>`;
        innerHtml += `<tr><td>价格:</td><td>$${currentPrice.toFixed(2)}</td></tr>`;
        
        // 如果有交易信息，添加交易信息
        if (trade) {
            const actionLabel = TRADE_ACTIONS[trade.action.toUpperCase()]?.label || trade.action;
            innerHtml += `<tr><td>操作:</td><td>${actionLabel}</td></tr>`;
            innerHtml += `<tr><td>数量:</td><td>${trade.quantity}</td></tr>`;
        }
        
        // 添加分析师信号
        if (analystSignalsHtml) {
            innerHtml += `<tr><td colspan="2"><div class="analyst-signals">${analystSignalsHtml}</div></td></tr>`;
        }
        
        innerHtml += '</table>';
        this.tooltipEl.querySelector('table').innerHTML = innerHtml;
    }

    formatDateForLookup(dateStr) {
        // 确保日期格式为 YYYY-MM-DD
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                console.warn(`无效的日期字符串: ${dateStr}`);
                return dateStr; // 如果无法解析，返回原始字符串
            }
            return date.toISOString().split('T')[0];
        } catch (e) {
            console.warn(`格式化日期出错: ${dateStr}`, e);
            return dateStr;
        }
    }

    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    getCommonScales(yAxisText) {
        return {
            x: {
                title: {
                    display: true,
                    text: '日期'
                }
            },
            y: {
                title: {
                    display: true,
                    text: yAxisText
                },
                ticks: {
                    callback: value => '$' + value.toLocaleString()
                }
            }
        };
    }

    handleNoStockData() {
        const container = document.querySelector('.stock-chart-container');
        if (container) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="bi bi-exclamation-circle fs-1"></i>
                    <p class="mt-3">没有${this.currentTicker ? ` ${this.currentTicker} 的` : '可用的'}价格数据</p>
                </div>`;
        }
    }

    handleError(message) {
        console.error(message);
        // 可以添加用户界面错误提示
    }

    createDecisionAnalystTable() {
        const container = document.getElementById('decisionAnalystContainer');
        if (!container) {
            console.warn('找不到决策分析师容器元素');
            return;
        }
        
        // 清空容器，防止重复添加内容
        container.innerHTML = '';
        
        // 如果没有数据，显示提示信息
        if (Object.keys(this.dailyDecisionsData).length === 0 && Object.keys(this.dailyAnalystSignalsData).length === 0) {
            container.innerHTML = '<div class="alert alert-info">没有可用的决策和分析师信号数据</div>';
            return;
        }
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.className = 'decision-analyst-content';
        contentContainer.id = 'decision-analyst-content';
        container.appendChild(contentContainer);
        
        // 获取所有日期
        const allDates = [...new Set([
            ...Object.keys(this.dailyAnalystSignalsData || {})
        ])].sort().reverse();
        
        // 初始化显示第一个日期的内容（最新日期）
        if (allDates.length > 0) {
            console.log('初始化显示最新日期:', allDates[0]);
            this.updateDecisionAnalystContent(contentContainer, allDates[0]);
        } else {
            contentContainer.innerHTML = '<div class="alert alert-info">没有可用的分析师信号日期数据</div>';
        }
    }
    
    updateDecisionAnalystContent(container, date) {
        console.log('更新内容，日期:', date, '当前股票:', this.currentTicker);
        
        if (!this.currentTicker) {
            container.innerHTML = '<div class="alert alert-info">请先选择一个股票</div>';
            return;
        }
        
        // 创建行容器，用于左右排列
        let html = '<div class="row">';
        
        // 添加决策部分（左侧）- 显示当前股票的所有日期决策
        html += '<div class="col-md-5">';
        html += '<div class="card h-100"><div class="card-header"><h5>交易决策</h5></div><div class="card-body">';
        
        // 收集当前股票的所有决策数据
        const allDecisions = [];
        Object.entries(this.dailyDecisionsData).forEach(([decisionDate, decisions]) => {
            if (decisions[this.currentTicker]) {
                allDecisions.push({
                    date: decisionDate,
                    decision: decisions[this.currentTicker]
                });
            }
        });
        
        if (allDecisions.length > 0) {
            html += '<div class="table-responsive"><table class="table table-striped">';
            html += '<thead><tr><th>日期</th><th>操作</th><th>数量</th><th>信心度</th></tr></thead><tbody>';
            
            // 按日期降序排序
            allDecisions.sort((a, b) => b.date.localeCompare(a.date));
            
            allDecisions.forEach(item => {
                const decision = item.decision;
                const action = decision.action || 'hold';
                const actionClass = action.toLowerCase();
                const actionLabel = TRADE_ACTIONS[action.toUpperCase()]?.label || action;
                const quantity = decision.quantity || '-';
                const confidence = decision.confidence ? `${decision.confidence}%` : '-';
                
                html += `<tr>
                    <td>${this.formatDate(item.date)}</td>
                    <td><span class="action-${actionClass}">${actionLabel}</span></td>
                    <td>${quantity}</td>
                    <td>${confidence}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
        } else {
            html += `<div class="alert alert-info">没有 ${this.currentTicker} 的交易决策数据</div>`;
        }
        
        html += '</div></div></div>';
        
        // 添加分析师信号部分（右侧）- 显示当前股票在选定日期的分析师信号
        html += '<div class="col-md-7">';
        html += '<div class="card h-100">';
        
        // 添加卡片头部，包含标题和日期选择器
        html += '<div class="card-header">';
        html += '<div class="d-flex justify-content-between align-items-center">';
        html += '<h5 class="mb-0">信号和决策理由</h5>';
        
        // 添加日期选择器到卡片头部
        html += '<div class="date-selector-inline">';
        html += '<select class="form-select form-select-sm" id="analyst-date-selector-inline">';
        
        // 获取所有日期并按倒序排序
        const allDates = [...new Set([
            ...Object.keys(this.dailyAnalystSignalsData || {})
        ])].sort().reverse();
        
        // 添加日期选项
        allDates.forEach(dateOption => {
            const selected = dateOption === date ? 'selected' : '';
            html += `<option value="${dateOption}" ${selected}>${this.formatDate(dateOption)}</option>`;
        });
        
        html += '</select>';
        html += '</div>'; // 关闭日期选择器容器
        html += '</div>'; // 关闭d-flex容器
        html += '</div>'; // 关闭卡片头部
        
        html += '<div class="card-body">';
        
        // 首先显示当前日期的决策理由（如果有）
        const currentDateDecisions = this.dailyDecisionsData[date] || {};
        const currentDecision = currentDateDecisions[this.currentTicker];

        if (currentDecision && currentDecision.reasoning) {
            html += '<div class="decision-reasoning-section mb-4">';
            html += `<h6 class="mb-2">决策理由 (${this.formatDate(date)})</h6>`;
            
            // 直接显示完整的决策理由，不使用折叠功能
            html += `
                <div class="reasoning-container p-2 bg-light rounded">
                    <div class="decision-reasoning-full">
                        ${currentDecision.reasoning}
                    </div>
                </div>
            `;
            html += '</div>';
        }
        
        // 尝试获取分析师信号数据
        const analystSignalsData = this.dailyAnalystSignalsData[date] || {};
        
        // 检查当前选中的股票是否有分析师信号
        let hasSignalsForCurrentTicker = false;
        if (this.currentTicker) {
            Object.values(analystSignalsData).forEach(signals => {
                if (signals && signals[this.currentTicker]) {
                    hasSignalsForCurrentTicker = true;
                }
            });
        }
        
        if (hasSignalsForCurrentTicker) {
            html += `<h6 class="mb-3">${this.formatDate(date)} ${this.currentTicker} 分析结果
                ${this.getCurrentPriceForDate(this.currentTicker, date)}
            </h6>`;
            html += '<table class="table table-sm"><thead><tr>';
            html += '<th style="min-width: 140px;">分析师</th>';
            html += '<th style="min-width: 80px;">信号</th>';
            html += '<th style="min-width: 80px;">信心度</th>';
            html += '<th>分析原因</th>';
            html += '</tr></thead><tbody>';
            
            // 添加该股票的所有分析师信号
            Object.entries(analystSignalsData).forEach(([analyst, signals]) => {
                if (signals && signals[this.currentTicker]) {
                    const signal = signals[this.currentTicker];
                    let signalText = '';
                    let signalClass = '';
                    
                    if (signal.signal === 'bullish') {
                        signalText = '看涨';
                        signalClass = 'signal-bullish';
                    } else if (signal.signal === 'bearish') {
                        signalText = '看跌';
                        signalClass = 'signal-bearish';
                    } else {
                        signalText = '中性';
                        signalClass = 'signal-neutral';
                    }
                    
                    const confidence = signal.confidence ? `${signal.confidence}%` : '-';
                    const analystName = this.analystNames[analyst] || analyst.replace('_agent', '').replace(/_/g, ' ');
                    
                    // 获取分析原因
                    let reasoning = '';
                    if (signal.reasoning) {
                        if (typeof signal.reasoning === 'string') {
                            reasoning = signal.reasoning;
                        } else if (typeof signal.reasoning === 'object') {
                            // 处理对象形式的推理数据
                            reasoning = this.formatReasoningObject(signal.reasoning);
                        }
                    } else if (signal.reason) {
                        reasoning = signal.reason;
                    } else if (signal.rationale) {
                        reasoning = signal.rationale;
                    }

                    // 创建可展开/收起的分析原因
                    let reasoningHtml = '';
                    if (reasoning) {
                        const reasoningId = `reasoning-${analyst}-${this.currentTicker}-${Date.now()}`;
                        
                        // 使用字符长度来判断是否需要折叠
                        // 一般来说，150个字符大约是两行文本
                        const needsExpansion = reasoning.length > 150;
                        
                        if (needsExpansion) {
                            // 内容需要折叠，显示展开按钮
                            reasoningHtml = `
                                <div class="reasoning-container">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="reasoning-content flex-grow-1">
                                            <div class="reasoning-truncated" id="${reasoningId}-truncated">
                                                ${reasoning}
                                            </div>
                                            <div class="reasoning-full" id="${reasoningId}-full" style="display: none;">
                                                ${reasoning}
                                            </div>
                                        </div>
                                        <span class="reasoning-toggle ms-2" id="${reasoningId}-toggle" onclick="event.stopPropagation(); toggleReasoning('${reasoningId}');">
                                            展开
                                        </span>
                                    </div>
                                </div>
                            `;
                            
                            // 添加一个延迟执行的函数，确保DOM元素已经创建
                            setTimeout(() => {
                                const truncatedEl = document.getElementById(`${reasoningId}-truncated`);
                                if (truncatedEl) {
                                    truncatedEl.addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        toggleReasoning(reasoningId);
                                    });
                                }
                            }, 100);
                        } else {
                            // 内容不需要折叠，直接显示
                            reasoningHtml = `<div>${reasoning}</div>`;
                        }
                    } else {
                        reasoningHtml = '<small class="text-muted">未提供分析原因</small>';
                    }

                    html += `<tr>
                        <td>${analystName}</td>
                        <td><span class="${signalClass}">${signalText}</span></td>
                        <td>${confidence}</td>
                        <td>${reasoningHtml}</td>
                    </tr>`;
                }
            });
            
            html += '</tbody></table>';
        } else {
            html += `<div class="alert alert-info">没有 ${this.currentTicker} 在 ${this.formatDate(date)} 的分析师信号数据</div>`;
        }
        
        html += '</div></div>'; // 关闭卡片体和卡片
        html += '</div>'; // 关闭右侧列
        
        // 关闭行容器
        html += '</div>';
        
        container.innerHTML = html;
        
        // 为内联日期选择器添加事件监听器
        const self = this;
        const inlineDateSelector = document.getElementById('analyst-date-selector-inline');
        if (inlineDateSelector) {
            inlineDateSelector.addEventListener('change', function() {
                const selectedDate = this.value;
                console.log('选择的日期:', selectedDate);
                self.updateDecisionAnalystContent(container, selectedDate);
            });
        }
    }

    // 格式化推理对象
    formatReasoningObject(reasoning) {
        if (!reasoning) return '';
        
        let result = '';
        
        // 处理不同类型的推理对象
        if (reasoning.details) {
            // 如果有details字段，直接使用
            result = reasoning.details;
        } else {
            // 遍历对象的所有属性
            for (const [key, value] of Object.entries(reasoning)) {
                const formattedKey = key.replace(/_/g, ' ');
                
                if (typeof value === 'object' && value !== null) {
                    if (value.signal && value.details) {
                        result += `<strong>${formattedKey}</strong>: ${value.details}<br>`;
                    } else {
                        const details = JSON.stringify(value).replace(/[{}"]/g, '');
                        result += `<strong>${formattedKey}</strong>: ${details}<br>`;
                    }
                } else {
                    result += `<strong>${formattedKey}</strong>: ${value}<br>`;
                }
            }
        }
        
        return result;
    }

    // 添加新方法来初始化交易历史过滤
    initTradeHistoryFilter() {
        // 获取交易历史表格
        const tradeHistoryTable = document.querySelector('#tradeHistoryCollapse table tbody');
        if (!tradeHistoryTable) {
            console.warn('找不到交易历史表格');
            return;
        }
        
        // 保存原始的所有行，以便在切换股票时恢复
        this.originalTradeRows = Array.from(tradeHistoryTable.querySelectorAll('tr'));
        
        // 初始过滤显示第一个股票的交易
        if (this.currentTicker) {
            this.filterTradeHistory(this.currentTicker);
        }
    }
    
    // 添加过滤交易历史的方法
    filterTradeHistory(ticker) {
        console.log(`过滤交易历史，显示 ${ticker} 的交易`);
        const tradeHistoryTable = document.querySelector('#tradeHistoryCollapse table tbody');
        if (!tradeHistoryTable || !this.originalTradeRows) return;
        
        // 清空当前表格
        tradeHistoryTable.innerHTML = '';
        
        // 过滤并添加匹配当前股票的行
        let hasMatchingTrades = false;
        
        this.originalTradeRows.forEach(row => {
            const tickerCell = row.querySelector('td:nth-child(2)');
            if (tickerCell && tickerCell.textContent === ticker) {
                tradeHistoryTable.appendChild(row.cloneNode(true));
                hasMatchingTrades = true;
            }
        });
        
        // 如果没有匹配的交易，显示提示信息
        if (!hasMatchingTrades) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="10" class="text-center">没有 ${ticker} 的交易记录</td>`;
            tradeHistoryTable.appendChild(emptyRow);
        }
    }

    // 获取特定日期的股票价格
    getCurrentPriceForDate(ticker, date) {
        // 尝试从风险管理代理获取价格
        const analystSignalsData = this.dailyAnalystSignalsData[date] || {};
        const riskManagerData = analystSignalsData['risk_management_agent'] || {};
        
        if (riskManagerData[ticker] && riskManagerData[ticker].current_price) {
            return ` <small class="text-muted">(价格: $${riskManagerData[ticker].current_price})</small>`;
        }
        
        // 如果风险管理代理没有价格数据，尝试从股票价格数据获取
        if (this.stockPricesData[ticker]) {
            const priceData = this.stockPricesData[ticker].find(item => item.date === date);
            if (priceData && priceData.price) {
                return ` <small class="text-muted">(价格: $${priceData.price.toFixed(2)})</small>`;
            }
        }
        
        return '';
    }
}

// 添加一个全局函数来检查BacktestChart是否可用
function isBacktestChartAvailable() {
    console.log('BacktestChart 类是否可用:', typeof BacktestChart !== 'undefined');
    return typeof BacktestChart !== 'undefined';
}

// 导出类以便在控制台中测试
window.BacktestChart = BacktestChart;
window.isBacktestChartAvailable = isBacktestChartAvailable;

// 在文件末尾添加日志
console.log('backtest_chart.js 已加载，BacktestChart 类已定义');

// 全局变量，存储 BacktestChart 实例
let backtestChartInstance = null;

// 添加全局函数用于切换分析原因的展开/收起状态
window.toggleReasoning = function(id) {
    console.log('toggleReasoning 被调用，id:', id);
    
    const truncated = document.getElementById(`${id}-truncated`);
    const full = document.getElementById(`${id}-full`);
    const toggle = document.getElementById(`${id}-toggle`);
    
    // 检查元素是否存在，防止错误
    if (!truncated || !full || !toggle) {
        console.error('找不到需要切换的元素:', id);
        return;
    }
    
    // 获取当前显示状态
    const isExpanded = window.getComputedStyle(full).display !== 'none';
    console.log('当前状态:', isExpanded ? '展开' : '收起');
    
    if (isExpanded) {
        // 当前是展开状态，切换到收起
        truncated.style.display = '';
        full.style.display = 'none';
        toggle.textContent = '展开';
        console.log('切换到收起状态');
    } else {
        // 当前是收起状态，切换到展开
        truncated.style.display = 'none';
        full.style.display = '';
        toggle.textContent = '收起';
        console.log('切换到展开状态');
    }
    
    // 阻止事件冒泡
    if (event) {
        event.stopPropagation();
    }
};

// 初始化图表
document.addEventListener('DOMContentLoaded', () => {
    // 添加内联日期选择器的样式
    const style = document.createElement('style');
    style.textContent = `
        .date-selector-inline {
            min-width: 120px;
            max-width: 180px;
        }
        .card-header .d-flex {
            gap: 10px;
        }
    `;
    document.head.appendChild(style);
    
    // 确保只创建一个实例
    if (!backtestChartInstance) {
        backtestChartInstance = new BacktestChart();
        backtestChartInstance.createDecisionAnalystTable();
        
        // 将实例暴露给全局，方便调试
        window.backtestChartInstance = backtestChartInstance;
    }
});

// 添加创建渐变色的辅助函数
function createGradient(p0, p1, color0, color1) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const gradient = ctx.createLinearGradient(p0.x, p0.y, p1.x, p1.y);
    gradient.addColorStop(0, color0);
    gradient.addColorStop(1, color1);
    return gradient;
}
