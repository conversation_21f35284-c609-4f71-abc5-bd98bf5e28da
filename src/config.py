import os

# Authelia配置
AUTHELIA_CONFIG = {
    'url': os.getenv('AUTHELIA_URL', 'https://sso.01sworld.top:8443'),
    'redirect_uris': [os.getenv('AUTHELIA_REDIRECT_URI', 'https://aifund.01sworld.top:8443/auth/callback')],
    'logout_redirect': os.getenv('AUTHELIA_LOGOUT_REDIRECT', 'https://aifund.01sworld.top:8443'),
    'client_id': os.getenv('AUTHELIA_CLIENT_ID', 'aifund'),
    'client_secret': os.getenv('AUTHELIA_CLIENT_SECRET', 'sd3ZBKEV30JbCywBcNGL52PI7Ar6gDpx1hvCH3SjIaaVT36HLoOl6Yvjd~KhaiEYlilX2_hv'),
    'scope': os.getenv('AUTHELIA_SCOPE', 'openid profile email groups'),
    'cookie_secure': os.getenv('COOKIE_SECURE', 'True').lower() == 'true',
    'cookie_domain': os.getenv('COOKIE_DOMAIN', '.01sworld.top'),
    'token_endpoint_auth_method': os.getenv('AUTHELIA_TOKEN_AUTH_METHOD', 'client_secret_basic'),
}

# 应用配置
APP_CONFIG = {
    'secret_key': os.getenv('SECRET_KEY', 'c92c04bf206c8ebacefde5d6f49142ea4d12f2f5'),
    'debug': os.getenv('DEV_MODE', 'False').lower() == 'true',
    'host': os.getenv('HOST', '0.0.0.0'),
    'port': int(os.getenv('PORT', 8888)),
    'dev_mode': os.getenv('DEV_MODE', 'False').lower() == 'true', 
}

# 股票配置
# 格式: (股票代码, 股票名称)
STOCK_OPTIONS = [
    ("NVDA", "NVIDIA"),
    ("MRNA", "Moderna"),
    ("PLTR", "Palantir"),
    ("TSLA", "Tesla"),
    ("GOOGL", "Google"),
]

STOCK_TO_MONITOR = [
    ("NVDA", "NVIDIA"),
    # ("MRNA", "Moderna"),
    ("TSLA", "Tesla"),
]

# 不需要Key的Tickers: AAPL, BRK.B, GOOGL, MSFT, NVDA, TSLA
NO_KEY_REQUIRED_TICKERS = ["AAPL", "BRK.B", "GOOGL", "MSFT", "NVDA", "TSLA"]

# 默认选中的股票
DEFAULT_STOCK = "NVDA"