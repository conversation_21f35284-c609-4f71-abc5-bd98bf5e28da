# Docker 部署指南

本文档提供了使用 Docker 部署 AI Fund 应用的详细说明。

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)

## 快速开始

### 1. 准备环境变量

确保在项目根目录下有 `.env` 文件，包含必要的 API 密钥：

```bash
# 复制示例环境变量文件并编辑
cp .env.example .env
# 编辑 .env 文件，填入您的 API 密钥
```

### 2. 使用 Docker Compose 启动应用

```bash
# 构建并启动容器
docker-compose up -d

# 查看日志
docker-compose logs -f
```

应用将在 http://localhost:8888 上运行。

### 3. 停止应用

```bash
docker-compose down
```

## 高级配置

### 持久化数据

PostgreSQL 数据存储在名为 `postgres_data` 的 Docker 卷中。即使容器被删除，数据也会保留。

### 自定义端口

如需更改默认端口 (5000)，请编辑 `docker-compose.yml` 文件中的 ports 部分：

```yaml
ports:
  - "8080:5000"  # 将主机的 8080 端口映射到容器的 5000 端口
```

### 环境变量

您可以在 `docker-compose.yml` 文件的 environment 部分添加或修改环境变量。

## 故障排除

### 数据库连接问题

如果应用无法连接到数据库，请确保：

1. 数据库容器已正常启动
2. 应用中的数据库连接字符串与 docker-compose.yml 中的配置一致

### 容器日志查看

```bash
# 查看应用容器日志
docker-compose logs app

# 查看数据库容器日志
docker-compose logs db
```

## 生产环境部署注意事项

在生产环境中部署时，请考虑以下安全措施：

1. 更改默认的数据库密码
2. 使用 Docker Secrets 或安全的环境变量管理方案管理敏感信息
3. 配置适当的备份策略
4. 考虑使用 HTTPS 保护 Web 界面