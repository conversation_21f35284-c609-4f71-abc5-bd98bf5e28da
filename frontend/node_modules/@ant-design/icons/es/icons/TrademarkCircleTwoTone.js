import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TrademarkCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/TrademarkCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
var TrademarkCircleTwoTone = function TrademarkCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TrademarkCircleTwoToneSvg
  }));
};

/**![trademark-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTE3MC43IDU4NC4yYy0xLjEuNS0yLjMuOC0zLjUuOGgtNjJjLTMuMSAwLTUuOS0xLjgtNy4yLTQuNmwtNzQuNi0xNTkuMmgtODguN1Y3MTdjMCA0LjQtMy42IDgtOCA4SDM4NGMtNC40IDAtOC0zLjYtOC04VjMwN2MwLTQuNCAzLjYtOCA4LThoMTU1LjZjOTguOCAwIDE0NC4yIDU5LjkgMTQ0LjIgMTMxLjEgMCA3MC4yLTQzLjYgMTA2LjQtNzguNCAxMTkuMmw4MC44IDE2NC4yYzIuMSAzLjkuNCA4LjctMy41IDEwLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MjkuOSAzNTdoLTgzLjR2MTQ4SDUyOGM1MyAwIDgyLjgtMjUuNiA4Mi44LTcyLjQgMC01MC4zLTMyLjktNzUuNi04MC45LTc1LjZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02MDUuNCA1NDkuM2MzNC44LTEyLjggNzguNC00OSA3OC40LTExOS4yIDAtNzEuMi00NS40LTEzMS4xLTE0NC4yLTEzMS4xSDM4NGMtNC40IDAtOCAzLjYtOCA4djQxMGMwIDQuNCAzLjYgOCA4IDhoNTQuN2M0LjQgMCA4LTMuNiA4LThWNTYxLjJoODguN0w2MTAgNzIwLjRjMS4zIDIuOCA0LjEgNC42IDcuMiA0LjZoNjJjMS4yIDAgMi40LS4zIDMuNS0uOCAzLjktMiA1LjYtNi44IDMuNS0xMC43bC04MC44LTE2NC4yek01MjggNTA1aC04MS41VjM1N2g4My40YzQ4IDAgODAuOSAyNS4zIDgwLjkgNzUuNiAwIDQ2LjgtMjkuOCA3Mi40LTgyLjggNzIuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(TrademarkCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TrademarkCircleTwoTone';
}
export default RefIcon;