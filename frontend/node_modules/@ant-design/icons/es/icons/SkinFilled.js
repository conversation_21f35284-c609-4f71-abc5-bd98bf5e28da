import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SkinFilledSvg from "@ant-design/icons-svg/es/asn/SkinFilled";
import AntdIcon from "../components/AntdIcon";
var SkinFilled = function SkinFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SkinFilledSvg
  }));
};

/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MCAxMjZINjYzLjhjLTE3LjQgMC0zMi45IDExLjktMzcgMjkuM0M2MTQuMyAyMDguMSA1NjcgMjQ2IDUxMiAyNDZzLTEwMi4zLTM3LjktMTE0LjgtOTAuN2EzNy45MyAzNy45MyAwIDAwLTM3LTI5LjNIMTU0YTQ0IDQ0IDAgMDAtNDQgNDR2MjUyYTQ0IDQ0IDAgMDA0NCA0NGg3NXYzODhhNDQgNDQgMCAwMDQ0IDQ0aDQ3OGE0NCA0NCAwIDAwNDQtNDRWNDY2aDc1YTQ0IDQ0IDAgMDA0NC00NFYxNzBhNDQgNDQgMCAwMC00NC00NHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(SkinFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SkinFilled';
}
export default RefIcon;