import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SketchSquareFilledSvg from "@ant-design/icons-svg/es/asn/SketchSquareFilled";
import AntdIcon from "../components/AntdIcon";
var SketchSquareFilled = function SketchSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SketchSquareFilledSvg
  }));
};

/**![sketch-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwOC4yIDQyMy4zTDUxMiAzMjYuMWwtOTYuMiA5Ny4yem0tMjUuOSAyMDIuM2wxNDcuOS0xNjYuM2gtNjMuNHptOTAtMjAyLjNoNjIuNWwtOTIuMS0xMTUuMXpNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tODEuMyAzMzIuMkw1MTUuOCA3NjIuM2MtMSAxLjEtMi40IDEuNy0zLjggMS43cy0yLjgtLjYtMy44LTEuN0wyMjUuMyA0NDQuMmE1LjE0IDUuMTQgMCAwMS0uMi02LjZMMzY1LjYgMjYyYzEtMS4yIDIuNC0xLjkgNC0xLjloMjg0LjZjMS42IDAgMyAuNyA0IDEuOWwxNDAuNSAxNzUuNmE0LjkgNC45IDAgMDEwIDYuNnptLTQwMS4xIDE1LjFMNTEyIDY4NC41bDExNC40LTIyNS4yem0tMTYuMy0xNTEuMWwtOTIuMSAxMTUuMWg2Mi41em0tODcuNSAxNTEuMWwxNDcuOSAxNjYuMy04NC41LTE2Ni4zem0xMjYuNS0xNTguMmwtMjMuMSA4OS44IDg4LjgtODkuOHptMTgzLjQgMEg1MzhsODguOCA4OS44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(SketchSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SketchSquareFilled';
}
export default RefIcon;