import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TagOutlinedSvg from "@ant-design/icons-svg/es/asn/TagOutlined";
import AntdIcon from "../components/AntdIcon";
var TagOutlined = function TagOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TagOutlinedSvg
  }));
};

/**![tag](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkzOCA0NTguOGwtMjkuNi0zMTIuNmMtMS41LTE2LjItMTQuNC0yOS0zMC42LTMwLjZMNTY1LjIgODZoLS40Yy0zLjIgMC01LjcgMS03LjYgMi45TDg4LjkgNTU3LjJhOS45NiA5Ljk2IDAgMDAwIDE0LjFsMzYzLjggMzYzLjhjMS45IDEuOSA0LjQgMi45IDcuMSAyLjlzNS4yLTEgNy4xLTIuOWw0NjguMy00NjguM2MyLTIuMSAzLTUgMi44LTh6TTQ1OS43IDgzNC43TDE4OS4zIDU2NC4zIDU4OSAxNjQuNiA4MzYgMTg4bDIzLjQgMjQ3LTM5OS43IDM5OS43ek02ODAgMjU2Yy00OC41IDAtODggMzkuNS04OCA4OHMzOS41IDg4IDg4IDg4IDg4LTM5LjUgODgtODgtMzkuNS04OC04OC04OHptMCAxMjBjLTE3LjcgMC0zMi0xNC4zLTMyLTMyczE0LjMtMzIgMzItMzIgMzIgMTQuMyAzMiAzMi0xNC4zIDMyLTMyIDMyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(TagOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TagOutlined';
}
export default RefIcon;