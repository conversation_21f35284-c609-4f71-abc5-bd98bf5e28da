import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SnippetsFilledSvg from "@ant-design/icons-svg/es/asn/SnippetsFilled";
import AntdIcon from "../components/AntdIcon";
var SnippetsFilled = function SnippetsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SnippetsFilledSvg
  }));
};

/**![snippets](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAxMTJINzI0VjcyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY0MEg1MDBWNzJjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djQwSDMyMGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTIwaC05NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NjMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUxMmMxNy43IDAgMzItMTQuMyAzMi0zMnYtOTZoOTZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyek02NjQgNDg2SDUxNFYzMzZoLjJMNjY0IDQ4NS44di4yem0xMjggMjc0aC01NlY0NTZMNTQ0IDI2NEgzNjB2LTgwaDY4djMyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTMyaDE1MnYzMmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di0zMmg2OHY1NzZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SnippetsFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SnippetsFilled';
}
export default RefIcon;