import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import VideoCameraAddOutlinedSvg from "@ant-design/icons-svg/es/asn/VideoCameraAddOutlined";
import AntdIcon from "../components/AntdIcon";
var VideoCameraAddOutlined = function VideoCameraAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: VideoCameraAddOutlinedSvg
  }));
};

/**![video-camera-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zNjggNzI0SDI1MlY2MDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djExNkg3MmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMTZ2MTE2YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNzg4aDExNmM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHoiIC8+PHBhdGggZD0iTTkxMiAzMDIuM0w3ODQgMzc2VjIyNGMwLTM1LjMtMjguNy02NC02NC02NEgxMjhjLTM1LjMgMC02NCAyOC43LTY0IDY0djM1Mmg3MlYyMzJoNTc2djU2MEg0NDh2NzJoMjcyYzM1LjMgMCA2NC0yOC43IDY0LTY0VjY0OGwxMjggNzMuN2MyMS4zIDEyLjMgNDgtMy4xIDQ4LTI3LjZWMzMwYzAtMjQuNi0yNi43LTQwLTQ4LTI3Ljd6TTg4OCA2MjVsLTEwNC01OS44VjQ1OC45TDg4OCAzOTl2MjI2eiIgLz48cGF0aCBkPSJNMzIwIDM2MGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgyMDhjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTEyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(VideoCameraAddOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VideoCameraAddOutlined';
}
export default RefIcon;