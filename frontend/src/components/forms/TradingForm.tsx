import React, { useState } from 'react'
import { 
  Form, 
  Select, 
  InputN<PERSON>ber, 
  DatePicker, 
  Checkbox, 
  Button, 
  Card, 
  Space,
  Row,
  Col,
  Switch,
  Input,
  TimePicker
} from 'antd'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { useDataStore } from '@/stores/useDataStore'
import { useTradingStore } from '@/stores/useTradingStore'
import { useTaskStore } from '@/stores/useTaskStore'
import type { TradeRequest, CreateTaskRequest } from '@/types'

const { Option } = Select

const TradingForm: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const [enableSchedule, setEnableSchedule] = useState(false)
  const [endCondition, setEndCondition] = useState<'never' | 'count' | 'date'>('never')
  
  const { stocks, analysts, models } = useDataStore()
  const { executeTrade, loading: tradingLoading } = useTradingStore()
  const { createTask, loading: taskLoading } = useTaskStore()

  const loading = tradingLoading || taskLoading

  const handleSubmit = async (values: any) => {
    try {
      if (enableSchedule) {
        // 创建定时任务
        const taskRequest: CreateTaskRequest = {
          name: values.task_name,
          tickers: values.tickers,
          analysts: values.analysts,
          model: values.model,
          initial_cash: values.initial_cash,
          margin_requirement: values.margin_requirement / 100, // 转换为小数
          show_reasoning: values.show_reasoning,
          execution_interval: values.execution_interval,
          end_condition: endCondition,
          end_count: endCondition === 'count' ? values.end_count : undefined,
          end_date: endCondition === 'date' ? values.end_date?.format('YYYY-MM-DD') : undefined,
          execution_time_start: values.execution_time_start?.format('HH:mm') || '21:30',
          execution_time_end: values.execution_time_end?.format('HH:mm') || '04:30',
          execute_immediately: values.execute_immediately,
        }
        
        await createTask(taskRequest)
        navigate('/tasks')
      } else {
        // 立即执行交易
        const tradeRequest: TradeRequest = {
          tickers: values.tickers,
          analysts: values.analysts,
          model: values.model,
          trading_date: values.trading_date?.toISOString(),
          initial_cash: values.initial_cash,
          margin_requirement: values.margin_requirement / 100, // 转换为小数
          show_reasoning: values.show_reasoning,
        }
        
        const record = await executeTrade(tradeRequest)
        navigate(`/record/${record.id}`)
      }
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        initial_cash: 100000,
        margin_requirement: 0,
        show_reasoning: true,
        execution_interval: '5m',
        execution_time_start: dayjs('21:30', 'HH:mm'),
        execution_time_end: dayjs('04:30', 'HH:mm'),
        execute_immediately: true,
      }}
    >
      {/* 基本设置 */}
      <Card title="基本设置" className="mb-6">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="tickers"
              label="股票代码（多选）"
              rules={[{ required: true, message: '请选择股票代码' }]}
            >
              <Select
                mode="multiple"
                placeholder="选择或输入股票代码"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {stocks.map(stock => (
                  <Option key={stock.code} value={stock.code}>
                    {stock.display}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="trading_date"
              label="交易日期"
              tooltip="默认为当前时间"
            >
              <DatePicker 
                showTime 
                style={{ width: '100%' }}
                placeholder="选择交易日期"
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="initial_cash"
              label="初始现金"
              rules={[{ required: true, message: '请输入初始现金' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1000}
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="margin_requirement"
              label="保证金要求 (%)"
              rules={[{ required: true, message: '请输入保证金要求' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={100}
                step={0.1}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item name="show_reasoning" valuePropName="checked">
          <Checkbox>显示推理过程</Checkbox>
        </Form.Item>
      </Card>

      {/* 选择分析师 */}
      <Card title="选择分析师" className="mb-6">
        <Form.Item
          name="analysts"
          rules={[{ required: true, message: '请至少选择一个分析师' }]}
        >
          <Checkbox.Group>
            <Row>
              {analysts.map(analyst => (
                <Col span={8} key={analyst.id}>
                  <Checkbox value={analyst.id} className="mb-2">
                    {analyst.display}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </Form.Item>
        
        <Space>
          <Button
            type="link"
            onClick={() => {
              const defaultAnalysts = ['technical_analyst', 'fundamentals_analyst', 'sentiment_analyst']
              form.setFieldValue('analysts', defaultAnalysts)
            }}
          >
            选择默认分析师
          </Button>
          <Button
            type="link"
            onClick={() => {
              form.setFieldValue('analysts', analysts.map(a => a.id))
            }}
          >
            全选
          </Button>
          <Button
            type="link"
            onClick={() => {
              form.setFieldValue('analysts', [])
            }}
          >
            取消全选
          </Button>
        </Space>
      </Card>

      {/* 选择模型 */}
      <Card title="选择模型" className="mb-6">
        <Form.Item
          name="model"
          label="LLM模型"
          rules={[{ required: true, message: '请选择LLM模型' }]}
        >
          <Select placeholder="请选择LLM模型">
            {models.map(model => (
              <Option key={model.id} value={model.id}>
                {model.display}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Card>

      {/* 定时执行 */}
      <Card title="定时执行" className="mb-6">
        <Form.Item label="启用定时执行">
          <Switch
            checked={enableSchedule}
            onChange={setEnableSchedule}
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        </Form.Item>
        
        {enableSchedule && (
          <div className="space-y-4">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="execution_interval"
                  label="执行频率"
                  rules={[{ required: true, message: '请选择执行频率' }]}
                >
                  <Select>
                    <Option value="1m">每分钟</Option>
                    <Option value="3m">每3分钟</Option>
                    <Option value="5m">每5分钟</Option>
                    <Option value="15m">每15分钟</Option>
                    <Option value="30m">每30分钟</Option>
                    <Option value="1h">每1小时</Option>
                    <Option value="4h">每4小时</Option>
                    <Option value="1d">每天</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="结束条件">
                  <Select value={endCondition} onChange={setEndCondition}>
                    <Option value="never">永不结束</Option>
                    <Option value="count">执行次数</Option>
                    <Option value="date">截止日期</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            
            {endCondition === 'count' && (
              <Form.Item
                name="end_count"
                label="执行次数"
                rules={[{ required: true, message: '请输入执行次数' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            )}
            
            {endCondition === 'date' && (
              <Form.Item
                name="end_date"
                label="截止日期"
                rules={[{ required: true, message: '请选择截止日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            )}
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="execution_time_start"
                  label="执行时段（开始）"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <TimePicker format="HH:mm" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="execution_time_end"
                  label="执行时段（结束）"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <TimePicker format="HH:mm" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item name="execute_immediately" valuePropName="checked">
              <Checkbox>立即执行</Checkbox>
            </Form.Item>
            
            <Form.Item
              name="task_name"
              label="任务名称"
            >
              <Input placeholder="可选，用于在队列中识别此任务" />
            </Form.Item>
          </div>
        )}
      </Card>

      {/* 提交按钮 */}
      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          size="large"
          block
        >
          {enableSchedule ? '创建定时任务' : '立即运行交易'}
        </Button>
      </Form.Item>
    </Form>
  )
}

export default TradingForm
