import React, { useState } from 'react'
import { 
  Form, 
  Select, 
  InputNumber, 
  DatePicker, 
  Checkbox, 
  Button, 
  Card, 
  Row,
  Col,
  Space,
  Alert
} from 'antd'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { useDataStore } from '@/stores/useDataStore'
import { useTradingStore } from '@/stores/useTradingStore'
import type { BacktestRequest } from '@/types'

const { Option } = Select
const { RangePicker } = DatePicker

const BacktestForm: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const [useDefaultPeriod, setUseDefaultPeriod] = useState(false)
  
  const { stocks, analysts, models } = useDataStore()
  const { executeBacktest, loading } = useTradingStore()

  const handleSubmit = async (values: any) => {
    try {
      let startDate: string
      let endDate: string
      
      if (useDefaultPeriod) {
        startDate = '2025-04-10'
        endDate = '2025-05-10'
      } else {
        if (!values.date_range || values.date_range.length !== 2) {
          throw new Error('请选择回测日期范围')
        }
        startDate = values.date_range[0].format('YYYY-MM-DD')
        endDate = values.date_range[1].format('YYYY-MM-DD')
      }
      
      const backtestRequest: BacktestRequest = {
        tickers: values.tickers,
        analysts: values.analysts,
        model: values.model,
        start_date: startDate,
        end_date: endDate,
        initial_cash: values.initial_cash,
        margin_requirement: values.margin_requirement / 100, // 转换为小数
        show_reasoning: values.show_reasoning,
        force_refresh: values.force_refresh,
        use_default_period: useDefaultPeriod,
      }
      
      const record = await executeBacktest(backtestRequest)
      navigate(`/record/${record.id}`)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        initial_cash: 100000,
        margin_requirement: 0,
        show_reasoning: true,
        force_refresh: false,
        date_range: [
          dayjs().subtract(1, 'month'),
          dayjs()
        ]
      }}
    >
      {/* 基本设置 */}
      <Card title="基本设置" className="mb-6">
        <Form.Item
          name="tickers"
          label="股票代码（多选）"
          rules={[{ required: true, message: '请选择股票代码' }]}
        >
          <Select
            mode="multiple"
            placeholder="选择或输入股票代码"
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {stocks.map(stock => (
              <Option key={stock.code} value={stock.code}>
                {stock.display}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item>
          <Checkbox
            checked={useDefaultPeriod}
            onChange={(e) => setUseDefaultPeriod(e.target.checked)}
          >
            <span className="inline-flex items-center">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
                测试
              </span>
              使用默认时间段 (2025.4.10 - 2025.5.10)
            </span>
          </Checkbox>
          <div className="text-xs text-gray-500 mt-1">
            此选项使用预设的测试数据时间段，适合快速验证系统功能
          </div>
        </Form.Item>
        
        {!useDefaultPeriod && (
          <Form.Item
            name="date_range"
            label="回测时间范围"
            rules={[{ required: true, message: '请选择回测时间范围' }]}
          >
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </Form.Item>
        )}
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="initial_cash"
              label="初始资本"
              rules={[{ required: true, message: '请输入初始资本' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1000}
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="margin_requirement"
              label="保证金要求 (%)"
              rules={[{ required: true, message: '请输入保证金要求' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={100}
                step={0.1}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item name="show_reasoning" valuePropName="checked">
          <Checkbox>显示推理过程</Checkbox>
        </Form.Item>
        
        <Form.Item name="force_refresh" valuePropName="checked">
          <Checkbox>强制刷新数据</Checkbox>
          <div className="text-xs text-gray-500 mt-1">
            启用后将忽略缓存，重新获取所有数据
          </div>
        </Form.Item>
      </Card>

      {/* 选择分析师 */}
      <Card title="选择分析师" className="mb-6">
        <Form.Item
          name="analysts"
          rules={[{ required: true, message: '请至少选择一个分析师' }]}
        >
          <Checkbox.Group>
            <Row>
              {analysts.map(analyst => (
                <Col span={8} key={analyst.id}>
                  <Checkbox value={analyst.id} className="mb-2">
                    {analyst.display}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </Form.Item>
        
        <Space>
          <Button
            type="link"
            onClick={() => {
              const defaultAnalysts = ['technical_analyst', 'fundamentals_analyst', 'sentiment_analyst']
              form.setFieldValue('analysts', defaultAnalysts)
            }}
          >
            选择默认分析师
          </Button>
          <Button
            type="link"
            onClick={() => {
              form.setFieldValue('analysts', analysts.map(a => a.id))
            }}
          >
            全选
          </Button>
          <Button
            type="link"
            onClick={() => {
              form.setFieldValue('analysts', [])
            }}
          >
            取消全选
          </Button>
        </Space>
      </Card>

      {/* 选择模型 */}
      <Card title="选择模型" className="mb-6">
        <Form.Item
          name="model"
          label="LLM模型"
          rules={[{ required: true, message: '请选择LLM模型' }]}
        >
          <Select placeholder="请选择LLM模型">
            {models.map(model => (
              <Option key={model.id} value={model.id}>
                {model.display}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Card>

      {/* 提交按钮 */}
      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          size="large"
          block
        >
          运行回测
        </Button>
      </Form.Item>
    </Form>
  )
}

export default BacktestForm
