@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义样式 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-content {
  padding: 24px;
  margin: 0;
  min-height: 280px;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 400px;
}

/* 加载动画样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 50px 20px;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 50px 20px;
  color: #999;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }
}
