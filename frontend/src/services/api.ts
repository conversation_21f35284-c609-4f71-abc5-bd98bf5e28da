import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { message } from 'antd'
import type {
  ApiResponse,
  User,
  LoginCredentials,
  Stock,
  PriceData,
  Analyst,
  Model,
  TradeRequest,
  BacktestRequest,
  TradingRecord,
  Task,
  CreateTaskRequest,
  TaskExecution,
  PaginatedResponse,
  DataMonitorStatus,
} from '@/types'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token')
          window.location.href = '/login'
        } else if (error.response?.status >= 500) {
          message.error('服务器错误，请稍后重试')
        } else if (error.response?.data?.message) {
          message.error(error.response.data.message)
        } else {
          message.error('网络错误，请检查网络连接')
        }
        return Promise.reject(error)
      }
    )
  }

  // 认证相关
  async login(credentials: LoginCredentials): Promise<{ user: User; token: string }> {
    const response = await this.api.post<ApiResponse<{ user: User; token: string }>>(
      '/auth/login',
      credentials
    )
    return response.data.data!
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout')
    localStorage.removeItem('token')
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<ApiResponse<User>>('/auth/me')
    return response.data.data!
  }

  // 配置相关
  async getStocks(): Promise<Stock[]> {
    const response = await this.api.get<ApiResponse<Stock[]>>('/config/stocks')
    return response.data.data!
  }

  async getAnalysts(): Promise<Analyst[]> {
    const response = await this.api.get<ApiResponse<Analyst[]>>('/config/analysts')
    return response.data.data!
  }

  async getModels(): Promise<Model[]> {
    const response = await this.api.get<ApiResponse<Model[]>>('/config/models')
    return response.data.data!
  }

  // 交易相关
  async executeTrade(request: TradeRequest): Promise<TradingRecord> {
    const response = await this.api.post<ApiResponse<TradingRecord>>('/trading/run', request)
    return response.data.data!
  }

  async executeBacktest(request: BacktestRequest): Promise<TradingRecord> {
    const response = await this.api.post<ApiResponse<TradingRecord>>('/trading/backtest', request)
    return response.data.data!
  }

  async getTradingHistory(page = 1, per_page = 20): Promise<PaginatedResponse<TradingRecord>> {
    const response = await this.api.get<ApiResponse<PaginatedResponse<TradingRecord>>>(
      '/trading/history',
      {
        params: { page, per_page },
      }
    )
    return response.data.data!
  }

  async getTradingRecord(id: string): Promise<TradingRecord> {
    const response = await this.api.get<ApiResponse<TradingRecord>>(`/trading/records/${id}`)
    return response.data.data!
  }

  // 任务管理
  async getTasks(): Promise<Task[]> {
    const response = await this.api.get<ApiResponse<Task[]>>('/tasks')
    return response.data.data!
  }

  async getTask(id: string): Promise<Task> {
    const response = await this.api.get<ApiResponse<Task>>(`/tasks/${id}`)
    return response.data.data!
  }

  async createTask(request: CreateTaskRequest): Promise<Task> {
    const response = await this.api.post<ApiResponse<Task>>('/tasks', request)
    return response.data.data!
  }

  async updateTask(id: string, updates: Partial<Task>): Promise<Task> {
    const response = await this.api.put<ApiResponse<Task>>(`/tasks/${id}`, updates)
    return response.data.data!
  }

  async deleteTask(id: string): Promise<void> {
    await this.api.delete(`/tasks/${id}`)
  }

  async pauseTask(id: string): Promise<void> {
    await this.api.post(`/tasks/${id}/pause`)
  }

  async resumeTask(id: string): Promise<void> {
    await this.api.post(`/tasks/${id}/resume`)
  }

  async getTaskExecutions(taskId: string): Promise<TaskExecution[]> {
    const response = await this.api.get<ApiResponse<TaskExecution[]>>(`/tasks/${taskId}/executions`)
    return response.data.data!
  }

  async getTaskStatus(): Promise<{ has_running: boolean; active_count: number }> {
    const response = await this.api.get<ApiResponse<{ has_running: boolean; active_count: number }>>(
      '/tasks/status'
    )
    return response.data.data!
  }

  // 数据相关
  async getStockPrices(ticker: string, start_date?: string, end_date?: string): Promise<PriceData[]> {
    const response = await this.api.get<ApiResponse<PriceData[]>>(`/data/prices/${ticker}`, {
      params: { start_date, end_date },
    })
    return response.data.data!
  }

  async refreshData(tickers?: string[]): Promise<void> {
    await this.api.post('/data/refresh', { tickers })
  }

  async getDataMonitorStatus(): Promise<DataMonitorStatus> {
    const response = await this.api.get<ApiResponse<DataMonitorStatus>>('/data/monitor')
    return response.data.data!
  }
}

export const apiService = new ApiService()
export default apiService
