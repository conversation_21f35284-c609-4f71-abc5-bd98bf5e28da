import React, { useEffect, useState } from 'react'
import { Card, Tabs, Table, Tag, Button, Space, Select, DatePicker, Input } from 'antd'
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { useTradingStore } from '@/stores/useTradingStore'
import { useDataStore } from '@/stores/useDataStore'
import type { TradingRecord } from '@/types'
import type { ColumnsType } from 'antd/es/table'

const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Search } = Input

const HistoryPage: React.FC = () => {
  const navigate = useNavigate()
  const { history, pagination, loading, fetchHistory } = useTradingStore()
  const { stocks } = useDataStore()
  
  const [activeTab, setActiveTab] = useState('backtest')
  const [filters, setFilters] = useState({
    ticker: 'all',
    search: '',
    dateRange: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
  })

  useEffect(() => {
    fetchHistory()
  }, [fetchHistory])

  const handleTableChange = (page: number, pageSize: number) => {
    fetchHistory(page, pageSize)
  }

  const handleRefresh = () => {
    fetchHistory(1, pagination.per_page)
  }

  const getStatusTag = (status: string) => {
    const statusMap = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getTypeTag = (type: string) => {
    const typeMap = {
      trading: { color: 'blue', text: '交易' },
      backtest: { color: 'green', text: '回测' },
    }
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 过滤数据
  const filteredData = history.filter(record => {
    // 类型过滤
    if (activeTab !== 'all' && record.type !== activeTab) {
      return false
    }
    
    // 股票过滤
    if (filters.ticker !== 'all' && !record.tickers.includes(filters.ticker)) {
      return false
    }
    
    // 搜索过滤
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const searchFields = [
        record.tickers.join(','),
        record.analysts.join(','),
        record.model,
      ].join(' ').toLowerCase()
      
      if (!searchFields.includes(searchLower)) {
        return false
      }
    }
    
    // 日期范围过滤
    if (filters.dateRange) {
      const recordDate = dayjs(record.trading_date)
      if (!recordDate.isBetween(filters.dateRange[0], filters.dateRange[1], 'day', '[]')) {
        return false
      }
    }
    
    return true
  })

  const columns: ColumnsType<TradingRecord> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => getTypeTag(type),
    },
    {
      title: '股票代码',
      dataIndex: 'tickers',
      key: 'tickers',
      width: 120,
      render: (tickers: string[]) => (
        <div>
          {tickers.map(ticker => (
            <Tag key={ticker} color="blue" className="mb-1">
              {ticker}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '分析师',
      dataIndex: 'analysts',
      key: 'analysts',
      width: 200,
      render: (analysts: string[]) => (
        <div>
          {analysts.slice(0, 2).map(analyst => (
            <Tag key={analyst} className="mb-1">
              {analyst.replace('_analyst', '').replace(/_/g, ' ')}
            </Tag>
          ))}
          {analysts.length > 2 && (
            <Tag className="mb-1">+{analysts.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '模型',
      dataIndex: 'model',
      key: 'model',
      width: 120,
    },
    {
      title: '交易日期',
      dataIndex: 'trading_date',
      key: 'trading_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '初始资金',
      dataIndex: 'initial_cash',
      key: 'initial_cash',
      width: 100,
      render: (cash: number) => `$${cash.toLocaleString()}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/record/${record.id}`)}
        >
          查看
        </Button>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">历史记录</h1>
          <p className="text-gray-600 mt-1">
            查看交易和回测的历史记录
          </p>
        </div>
        <Button
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      <Card>
        {/* 筛选器 */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Search
                placeholder="搜索股票代码、分析师或模型"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                allowClear
              />
            </div>
            
            <Select
              value={filters.ticker}
              onChange={(value) => setFilters(prev => ({ ...prev, ticker: value }))}
              style={{ width: 150 }}
              placeholder="选择股票"
            >
              <Select.Option value="all">全部股票</Select.Option>
              {stocks.map(stock => (
                <Select.Option key={stock.code} value={stock.code}>
                  {stock.code}
                </Select.Option>
              ))}
            </Select>
            
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
              placeholder={['开始日期', '结束日期']}
            />
            
            <Button
              onClick={() => setFilters({ ticker: 'all', search: '', dateRange: null })}
            >
              重置
            </Button>
          </div>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'backtest',
              label: '回测记录',
            },
            {
              key: 'trading',
              label: '交易记录',
            },
            {
              key: 'all',
              label: '全部记录',
            },
          ]}
        />

        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.per_page,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default HistoryPage
