import React, { useEffect } from 'react'
import { Card, Button, Statistic, Alert, Space, Tag } from 'antd'
import { ReloadOutlined, DatabaseOutlined, ClockCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { useDataStore } from '@/stores/useDataStore'

const DataMonitorPage: React.FC = () => {
  const { 
    monitorStatus, 
    stocks, 
    loading, 
    refreshing, 
    fetchMonitorStatus, 
    refreshData 
  } = useDataStore()

  useEffect(() => {
    fetchMonitorStatus()
    
    // 设置定时刷新状态
    const interval = setInterval(fetchMonitorStatus, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [fetchMonitorStatus])

  const handleRefreshAll = async () => {
    try {
      await refreshData()
      await fetchMonitorStatus()
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleRefreshSpecific = async (tickers: string[]) => {
    try {
      await refreshData(tickers)
      await fetchMonitorStatus()
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const getCacheStatusTag = (status: string) => {
    const statusMap = {
      healthy: { color: 'success', text: '正常' },
      warning: { color: 'warning', text: '警告' },
      error: { color: 'error', text: '错误' },
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据监控</h1>
          <p className="text-gray-600 mt-1">
            监控系统数据状态和缓存情况
          </p>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchMonitorStatus}
            loading={loading}
          >
            刷新状态
          </Button>
          <Button
            type="primary"
            icon={<DatabaseOutlined />}
            onClick={handleRefreshAll}
            loading={refreshing}
          >
            刷新所有数据
          </Button>
        </Space>
      </div>

      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <Statistic
            title="股票数量"
            value={stocks.length}
            prefix={<DatabaseOutlined />}
          />
        </Card>
        
        <Card>
          <Statistic
            title="价格数据点"
            value={monitorStatus?.prices_count || 0}
            prefix={<ClockCircleOutlined />}
          />
        </Card>
        
        <Card>
          <Statistic
            title="缓存状态"
            value=""
            formatter={() => getCacheStatusTag(monitorStatus?.cache_status || 'unknown')}
          />
        </Card>
        
        <Card>
          <Statistic
            title="最后刷新"
            value={monitorStatus?.last_refresh ? dayjs(monitorStatus.last_refresh).fromNow() : '未知'}
            prefix={<ClockCircleOutlined />}
          />
        </Card>
      </div>

      {/* 刷新状态提示 */}
      {monitorStatus?.refresh_in_progress && (
        <Alert
          message="数据刷新中"
          description="系统正在刷新数据，请稍候..."
          type="info"
          showIcon
        />
      )}

      {/* 数据详情 */}
      <Card title="数据详情">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">监控的股票</h4>
            <div className="flex flex-wrap gap-2">
              {stocks.map(stock => (
                <Tag 
                  key={stock.code}
                  className="cursor-pointer"
                  onClick={() => handleRefreshSpecific([stock.code])}
                >
                  {stock.code} - {stock.name}
                </Tag>
              ))}
            </div>
          </div>
          
          {monitorStatus && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">系统信息</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">最后刷新时间:</span>
                    <span>{dayjs(monitorStatus.last_refresh).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">股票数量:</span>
                    <span>{monitorStatus.stocks_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">价格数据点:</span>
                    <span>{monitorStatus.prices_count.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">缓存状态:</span>
                    <span>{getCacheStatusTag(monitorStatus.cache_status)}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">操作</h4>
                <div className="space-y-2">
                  <Button
                    block
                    onClick={handleRefreshAll}
                    loading={refreshing}
                    disabled={monitorStatus.refresh_in_progress}
                  >
                    刷新所有数据
                  </Button>
                  <Button
                    block
                    onClick={() => handleRefreshSpecific(stocks.slice(0, 5).map(s => s.code))}
                    loading={refreshing}
                    disabled={monitorStatus.refresh_in_progress}
                  >
                    刷新前5只股票
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明">
        <div className="space-y-2 text-sm text-gray-600">
          <p>• 系统会自动缓存股票价格数据以提高性能</p>
          <p>• 可以手动刷新数据以获取最新信息</p>
          <p>• 缓存状态显示系统数据的健康程度</p>
          <p>• 点击股票标签可以单独刷新该股票的数据</p>
        </div>
      </Card>
    </div>
  )
}

export default DataMonitorPage
