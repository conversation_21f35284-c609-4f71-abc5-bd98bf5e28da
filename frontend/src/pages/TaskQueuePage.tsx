import React, { useEffect } from 'react'
import { Card, Table, Tag, Button, Space, Modal, message } from 'antd'
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  ReloadOutlined 
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { useTaskStore } from '@/stores/useTaskStore'
import type { Task } from '@/types'
import type { ColumnsType } from 'antd/es/table'

const TaskQueuePage: React.FC = () => {
  const navigate = useNavigate()
  const { 
    tasks, 
    loading, 
    fetchTasks, 
    pauseTask, 
    resumeTask, 
    deleteTask 
  } = useTaskStore()

  useEffect(() => {
    fetchTasks()
    
    // 设置定时刷新
    const interval = setInterval(fetchTasks, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [fetchTasks])

  const handlePauseTask = async (taskId: string) => {
    try {
      await pauseTask(taskId)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleResumeTask = async (taskId: string) => {
    try {
      await resumeTask(taskId)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleDeleteTask = (taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此任务吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteTask(taskId)
        } catch (error) {
          // 错误已在store中处理
        }
      },
    })
  }

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '运行中' },
      paused: { color: 'warning', text: '已暂停' },
      completed: { color: 'blue', text: '已完成' },
      error: { color: 'error', text: '错误' },
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const columns: ColumnsType<Task> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => name || '未命名任务',
    },
    {
      title: '股票代码',
      dataIndex: 'tickers',
      key: 'tickers',
      render: (tickers: string[]) => tickers.join(', '),
    },
    {
      title: '执行频率',
      dataIndex: 'execution_interval',
      key: 'execution_interval',
    },
    {
      title: '下次执行',
      dataIndex: 'next_run',
      key: 'next_run',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '执行次数',
      dataIndex: 'execution_count',
      key: 'execution_count',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.status === 'active' && (
            <Button
              type="text"
              icon={<PauseCircleOutlined />}
              onClick={() => handlePauseTask(record.id)}
              title="暂停任务"
            />
          )}
          {record.status === 'paused' && (
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              onClick={() => handleResumeTask(record.id)}
              title="恢复任务"
            />
          )}
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/tasks/${record.id}`)}
            title="查看详情"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTask(record.id)}
            title="删除任务"
          />
        </Space>
      ),
    },
  ]

  // 按状态排序：运行中 > 暂停 > 已完成 > 错误
  const sortedTasks = [...tasks].sort((a, b) => {
    const statusOrder = { active: 0, paused: 1, completed: 2, error: 3 }
    return (statusOrder[a.status as keyof typeof statusOrder] || 4) - 
           (statusOrder[b.status as keyof typeof statusOrder] || 4)
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务队列</h1>
          <p className="text-gray-600 mt-1">
            管理定时执行的交易任务
          </p>
        </div>
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchTasks}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      <Card>
        {tasks.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无定时任务</h3>
            <p className="text-gray-500 mb-4">您可以在交易页面创建定时执行的任务</p>
            <Button type="primary" onClick={() => navigate('/')}>
              创建任务
            </Button>
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={sortedTasks}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条任务`,
            }}
          />
        )}
      </Card>
    </div>
  )
}

export default TaskQueuePage
