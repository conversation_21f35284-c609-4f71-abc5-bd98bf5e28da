import React, { useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Descriptions, Button, Space, Tag, Spin, Alert } from 'antd'
import { ArrowLeftOutlined, PlayCircleOutlined, PauseCircleOutlined, DeleteOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { useTaskStore } from '@/stores/useTaskStore'

const TaskDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>()
  const navigate = useNavigate()
  const { 
    currentTask, 
    executions, 
    loading, 
    fetchTask, 
    fetchTaskExecutions,
    pauseTask,
    resumeTask,
    deleteTask,
    clearCurrentTask 
  } = useTaskStore()

  useEffect(() => {
    if (taskId) {
      fetchTask(taskId)
      fetchTaskExecutions(taskId)
    }
    
    return () => {
      clearCurrentTask()
    }
  }, [taskId, fetchTask, fetchTaskExecutions, clearCurrentTask])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  if (!currentTask) {
    return (
      <Alert
        message="任务不存在"
        description="请检查任务ID是否正确"
        type="error"
        showIcon
      />
    )
  }

  const handlePauseTask = async () => {
    if (taskId) {
      try {
        await pauseTask(taskId)
      } catch (error) {
        // 错误已在store中处理
      }
    }
  }

  const handleResumeTask = async () => {
    if (taskId) {
      try {
        await resumeTask(taskId)
      } catch (error) {
        // 错误已在store中处理
      }
    }
  }

  const handleDeleteTask = async () => {
    if (taskId) {
      try {
        await deleteTask(taskId)
        navigate('/tasks')
      } catch (error) {
        // 错误已在store中处理
      }
    }
  }

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '运行中' },
      paused: { color: 'warning', text: '已暂停' },
      completed: { color: 'blue', text: '已完成' },
      error: { color: 'error', text: '错误' },
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getEndConditionText = (task: typeof currentTask) => {
    if (task.end_condition === 'never') {
      return '永不结束'
    } else if (task.end_condition === 'count') {
      return `执行 ${task.end_count} 次后结束`
    } else if (task.end_condition === 'date') {
      return `在 ${task.end_date} 后结束`
    }
    return '未知'
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/tasks')}
          >
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {currentTask.name || '未命名任务'}
            </h1>
            <p className="text-gray-600 mt-1">
              任务详情和执行记录
            </p>
          </div>
        </div>
        
        <Space>
          {currentTask.status === 'active' && (
            <Button
              icon={<PauseCircleOutlined />}
              onClick={handlePauseTask}
            >
              暂停任务
            </Button>
          )}
          {currentTask.status === 'paused' && (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleResumeTask}
            >
              恢复任务
            </Button>
          )}
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={handleDeleteTask}
          >
            删除任务
          </Button>
        </Space>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 基本信息 */}
        <Card title="基本信息">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="任务名称">
              {currentTask.name || '未命名任务'}
            </Descriptions.Item>
            <Descriptions.Item label="股票代码">
              {currentTask.tickers.join(', ')}
            </Descriptions.Item>
            <Descriptions.Item label="执行频率">
              {currentTask.execution_interval}
            </Descriptions.Item>
            <Descriptions.Item label="结束条件">
              {getEndConditionText(currentTask)}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {getStatusTag(currentTask.status)}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 执行信息 */}
        <Card title="执行信息">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="创建时间">
              {dayjs(currentTask.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="下次执行">
              {currentTask.next_run 
                ? dayjs(currentTask.next_run).format('YYYY-MM-DD HH:mm:ss')
                : '-'
              }
            </Descriptions.Item>
            <Descriptions.Item label="已执行次数">
              {currentTask.execution_count}
            </Descriptions.Item>
            <Descriptions.Item label="最后执行">
              {currentTask.last_execution 
                ? dayjs(currentTask.last_execution).format('YYYY-MM-DD HH:mm:ss')
                : '-'
              }
            </Descriptions.Item>
            <Descriptions.Item label="执行时段">
              {currentTask.execution_time_start} - {currentTask.execution_time_end}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>

      {/* 任务配置 */}
      <Card title="任务配置">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="初始现金">
              ${currentTask.initial_cash.toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="保证金要求">
              {(currentTask.margin_requirement * 100).toFixed(1)}%
            </Descriptions.Item>
            <Descriptions.Item label="显示推理过程">
              {currentTask.show_reasoning ? '是' : '否'}
            </Descriptions.Item>
          </Descriptions>
          
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">分析师</h4>
            <div className="space-y-1">
              {currentTask.analysts.map(analyst => (
                <Tag key={analyst}>
                  {analyst.replace('_analyst', '').replace(/_/g, ' ')}
                </Tag>
              ))}
            </div>
          </div>
          
          <Descriptions column={1} size="small">
            <Descriptions.Item label="模型">
              {currentTask.model}
            </Descriptions.Item>
            <Descriptions.Item label="立即执行">
              {currentTask.execute_immediately ? '是' : '否'}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Card>

      {/* 执行记录 */}
      <Card title="执行记录">
        {executions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无执行记录
          </div>
        ) : (
          <div className="space-y-4">
            {executions.map(execution => (
              <div key={execution.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">
                    {dayjs(execution.execution_time).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                  {getStatusTag(execution.status)}
                </div>
                {execution.error_message && (
                  <Alert
                    message="执行错误"
                    description={execution.error_message}
                    type="error"
                    size="small"
                  />
                )}
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  )
}

export default TaskDetailPage
