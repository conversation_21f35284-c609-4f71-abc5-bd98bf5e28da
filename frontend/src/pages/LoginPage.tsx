import React from 'react'
import { Form, Input, But<PERSON>, Card, Typography, message } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useUserStore } from '@/stores/useUserStore'
import type { LoginCredentials } from '@/types'

const { Title, Text } = Typography

const LoginPage: React.FC = () => {
  const { login, loading } = useUserStore()
  const [form] = Form.useForm()

  const handleSubmit = async (values: LoginCredentials) => {
    try {
      await login(values)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4">
            <span className="text-white text-2xl font-bold">AI</span>
          </div>
          <Title level={2} className="text-gray-900">
            AI Fund
          </Title>
          <Text type="secondary">
            智能投资基金管理系统
          </Text>
        </div>

        <Card className="shadow-lg">
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <div className="text-center text-sm text-gray-500 mt-4">
            <Text type="secondary">
              开发模式下可使用任意用户名和密码登录
            </Text>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default LoginPage
