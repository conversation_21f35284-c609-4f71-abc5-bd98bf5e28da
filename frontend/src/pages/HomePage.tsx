import React, { useState } from 'react'
import { Tabs, Card, Alert } from 'antd'
import { TradingOutlined, BarChartOutlined } from '@ant-design/icons'
import TradingForm from '@/components/forms/TradingForm'
import BacktestForm from '@/components/forms/BacktestForm'
import { useTaskStore } from '@/stores/useTaskStore'

const { TabPane } = Tabs

const HomePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('trading')
  const { tasks } = useTaskStore()

  // 计算活跃任务数量
  const activeTasks = tasks.filter(task => task.status === 'active').length

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Fund 交易系统</h1>
          <p className="text-gray-600 mt-1">
            使用AI分析师进行智能交易决策和回测分析
          </p>
        </div>
      </div>

      {/* 活跃任务提示 */}
      {activeTasks > 0 && (
        <Alert
          message={`当前有 ${activeTasks} 个活跃任务正在运行`}
          type="info"
          showIcon
          closable
        />
      )}

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          items={[
            {
              key: 'trading',
              label: (
                <span>
                  <TradingOutlined />
                  交易
                </span>
              ),
              children: <TradingForm />,
            },
            {
              key: 'backtest',
              label: (
                <span>
                  <BarChartOutlined />
                  回测
                </span>
              ),
              children: <BacktestForm />,
            },
          ]}
        />
      </Card>
    </div>
  )
}

export default HomePage
