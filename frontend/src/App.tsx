import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import AppLayout from './components/layout/AppLayout'
import HomePage from './pages/HomePage'
import HistoryPage from './pages/HistoryPage'
import TaskQueuePage from './pages/TaskQueuePage'
import TaskDetailPage from './pages/TaskDetailPage'
import DataMonitorPage from './pages/DataMonitorPage'
import LoginPage from './pages/LoginPage'
import { useUserStore } from './stores/useUserStore'

const { Content } = Layout

function App() {
  const { isAuthenticated } = useUserStore()

  // 如果未认证，显示登录页面
  if (!isAuthenticated) {
    return <LoginPage />
  }

  return (
    <AppLayout>
      <Content>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/history" element={<HistoryPage />} />
          <Route path="/tasks" element={<TaskQueuePage />} />
          <Route path="/tasks/:taskId" element={<TaskDetailPage />} />
          <Route path="/data-monitor" element={<DataMonitorPage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Content>
    </AppLayout>
  )
}

export default App
