// 用户相关类型
export interface User {
  id: string
  username: string
  email?: string
  avatar?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

// 股票相关类型
export interface Stock {
  code: string
  name: string
  display: string
}

export interface PriceData {
  date: string
  price: number
  volume?: number
}

// 分析师相关类型
export interface Analyst {
  id: string
  name: string
  display: string
  description?: string
}

export interface AnalystSignal {
  signal: 'bullish' | 'bearish' | 'neutral'
  confidence?: number
  reasoning?: string
  timestamp: string
}

// 模型相关类型
export interface Model {
  id: string
  name: string
  display: string
  provider: string
}

// 交易相关类型
export interface TradeRequest {
  tickers: string[]
  analysts: string[]
  model: string
  trading_date?: string
  initial_cash: number
  margin_requirement: number
  show_reasoning: boolean
}

export interface BacktestRequest extends TradeRequest {
  start_date: string
  end_date: string
  force_refresh?: boolean
  use_default_period?: boolean
}

export interface TradingRecord {
  id: string
  type: 'trading' | 'backtest'
  tickers: string[]
  analysts: string[]
  model: string
  trading_date: string
  initial_cash: number
  margin_requirement: number
  show_reasoning: boolean
  status: 'running' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  decisions?: Record<string, any>
  analyst_signals?: Record<string, any>
  execution_log?: string
  metrics?: Record<string, any>
}

// 任务相关类型
export interface Task {
  id: string
  name?: string
  tickers: string[]
  analysts: string[]
  model: string
  initial_cash: number
  margin_requirement: number
  show_reasoning: boolean
  execution_interval: string
  end_condition: 'never' | 'count' | 'date'
  end_count?: number
  end_date?: string
  execution_time_start: string
  execution_time_end: string
  execute_immediately: boolean
  status: 'active' | 'paused' | 'completed' | 'error'
  created_at: string
  next_run?: string
  execution_count: number
  last_execution?: string
  error_message?: string
}

export interface CreateTaskRequest {
  name?: string
  tickers: string[]
  analysts: string[]
  model: string
  initial_cash: number
  margin_requirement: number
  show_reasoning: boolean
  execution_interval: string
  end_condition: 'never' | 'count' | 'date'
  end_count?: number
  end_date?: string
  execution_time_start: string
  execution_time_end: string
  execute_immediately: boolean
}

// 任务执行记录
export interface TaskExecution {
  id: string
  task_id: string
  execution_time: string
  status: 'running' | 'completed' | 'failed'
  decisions?: Record<string, any>
  analyst_signals?: Record<string, any>
  execution_log?: string
  error_message?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  per_page: number
  pages: number
}

// 图表相关类型
export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
}

export interface ChartDataset {
  label: string
  data: number[]
  borderColor?: string
  backgroundColor?: string
  fill?: boolean
}

// 表单相关类型
export interface FormErrors {
  [key: string]: string | undefined
}

// 通用状态类型
export interface LoadingState {
  loading: boolean
  error?: string
}

// 筛选和分页类型
export interface FilterOptions {
  ticker?: string
  status?: string
  date_range?: [string, string]
}

export interface PaginationOptions {
  page: number
  per_page: number
}

// 数据监控类型
export interface DataMonitorStatus {
  last_refresh: string
  stocks_count: number
  prices_count: number
  cache_status: 'healthy' | 'warning' | 'error'
  refresh_in_progress: boolean
}
