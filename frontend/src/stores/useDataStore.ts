import { create } from 'zustand'
import { message } from 'antd'
import apiService from '@/services/api'
import type { Stock, PriceData, Analyst, Model, DataMonitorStatus } from '@/types'

interface DataState {
  stocks: Stock[]
  prices: Record<string, PriceData[]>
  analysts: Analyst[]
  models: Model[]
  monitorStatus: DataMonitorStatus | null
  loading: boolean
  refreshing: boolean
  error: string | null
  
  // Actions
  fetchStocks: () => Promise<void>
  fetchAnalysts: () => Promise<void>
  fetchModels: () => Promise<void>
  fetchPrices: (ticker: string, start_date?: string, end_date?: string) => Promise<void>
  fetchMonitorStatus: () => Promise<void>
  refreshData: (tickers?: string[]) => Promise<void>
  clearError: () => void
}

export const useDataStore = create<DataState>((set, get) => ({
  stocks: [],
  prices: {},
  analysts: [],
  models: [],
  monitorStatus: null,
  loading: false,
  refreshing: false,
  error: null,

  fetchStocks: async () => {
    set({ loading: true, error: null })
    try {
      const stocks = await apiService.getStocks()
      set({ stocks, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取股票列表失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchAnalysts: async () => {
    set({ loading: true, error: null })
    try {
      const analysts = await apiService.getAnalysts()
      set({ analysts, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取分析师列表失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchModels: async () => {
    set({ loading: true, error: null })
    try {
      const models = await apiService.getModels()
      set({ models, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取模型列表失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchPrices: async (ticker: string, start_date?: string, end_date?: string) => {
    set({ loading: true, error: null })
    try {
      const priceData = await apiService.getStockPrices(ticker, start_date, end_date)
      const { prices } = get()
      set({ 
        prices: { ...prices, [ticker]: priceData },
        loading: false 
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || `获取${ticker}价格数据失败`
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchMonitorStatus: async () => {
    set({ loading: true, error: null })
    try {
      const monitorStatus = await apiService.getDataMonitorStatus()
      set({ monitorStatus, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取数据监控状态失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  refreshData: async (tickers?: string[]) => {
    set({ refreshing: true, error: null })
    try {
      await apiService.refreshData(tickers)
      message.success('数据刷新成功')
      
      // 刷新监控状态
      const { fetchMonitorStatus } = get()
      await fetchMonitorStatus()
      
      set({ refreshing: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '数据刷新失败'
      set({ error: errorMessage, refreshing: false })
      message.error(errorMessage)
      throw error
    }
  },

  clearError: () => {
    set({ error: null })
  },
}))

// 初始化时加载基础数据
const initializeData = async () => {
  const { fetchStocks, fetchAnalysts, fetchModels } = useDataStore.getState()
  try {
    await Promise.all([
      fetchStocks(),
      fetchAnalysts(),
      fetchModels(),
    ])
  } catch (error) {
    console.error('Failed to initialize data:', error)
  }
}

// 延迟初始化，确保store已经创建
setTimeout(initializeData, 100)
