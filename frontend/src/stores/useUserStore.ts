import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { message } from 'antd'
import apiService from '@/services/api'
import type { User, LoginCredentials } from '@/types'

interface UserState {
  user: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  fetchUser: () => Promise<void>
  clearError: () => void
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ loading: true, error: null })
        try {
          const { user, token } = await apiService.login(credentials)
          localStorage.setItem('token', token)
          set({ 
            user, 
            isAuthenticated: true, 
            loading: false 
          })
          message.success('登录成功')
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || '登录失败'
          set({ 
            error: errorMessage, 
            loading: false,
            isAuthenticated: false,
            user: null
          })
          message.error(errorMessage)
          throw error
        }
      },

      logout: async () => {
        set({ loading: true })
        try {
          await apiService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          localStorage.removeItem('token')
          set({ 
            user: null, 
            isAuthenticated: false, 
            loading: false,
            error: null
          })
          message.success('已退出登录')
        }
      },

      fetchUser: async () => {
        const token = localStorage.getItem('token')
        if (!token) {
          set({ isAuthenticated: false, user: null })
          return
        }

        set({ loading: true, error: null })
        try {
          const user = await apiService.getCurrentUser()
          set({ 
            user, 
            isAuthenticated: true, 
            loading: false 
          })
        } catch (error: any) {
          localStorage.removeItem('token')
          set({ 
            user: null, 
            isAuthenticated: false, 
            loading: false,
            error: error.response?.data?.message || '获取用户信息失败'
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'user-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// 初始化时检查用户状态
const token = localStorage.getItem('token')
if (token) {
  useUserStore.getState().fetchUser()
}
