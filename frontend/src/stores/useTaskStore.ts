import { create } from 'zustand'
import { message } from 'antd'
import apiService from '@/services/api'
import type { Task, CreateTaskRequest, TaskExecution } from '@/types'

interface TaskState {
  tasks: Task[]
  currentTask: Task | null
  executions: TaskExecution[]
  loading: boolean
  error: string | null
  
  // Actions
  fetchTasks: () => Promise<void>
  fetchTask: (id: string) => Promise<void>
  createTask: (request: CreateTaskRequest) => Promise<Task>
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>
  deleteTask: (id: string) => Promise<void>
  pauseTask: (id: string) => Promise<void>
  resumeTask: (id: string) => Promise<void>
  fetchTaskExecutions: (taskId: string) => Promise<void>
  getTaskStatus: () => Promise<{ has_running: boolean; active_count: number }>
  clearError: () => void
  clearCurrentTask: () => void
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  currentTask: null,
  executions: [],
  loading: false,
  error: null,

  fetchTasks: async () => {
    set({ loading: true, error: null })
    try {
      const tasks = await apiService.getTasks()
      set({ tasks, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取任务列表失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchTask: async (id: string) => {
    set({ loading: true, error: null })
    try {
      const task = await apiService.getTask(id)
      set({ currentTask: task, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取任务详情失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  createTask: async (request: CreateTaskRequest) => {
    set({ loading: true, error: null })
    try {
      const task = await apiService.createTask(request)
      const { tasks } = get()
      set({ 
        tasks: [task, ...tasks], 
        loading: false 
      })
      message.success('任务创建成功')
      return task
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '创建任务失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
      throw error
    }
  },

  updateTask: async (id: string, updates: Partial<Task>) => {
    set({ loading: true, error: null })
    try {
      const updatedTask = await apiService.updateTask(id, updates)
      const { tasks, currentTask } = get()
      
      set({
        tasks: tasks.map(task => task.id === id ? updatedTask : task),
        currentTask: currentTask?.id === id ? updatedTask : currentTask,
        loading: false
      })
      message.success('任务更新成功')
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '更新任务失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
      throw error
    }
  },

  deleteTask: async (id: string) => {
    set({ loading: true, error: null })
    try {
      await apiService.deleteTask(id)
      const { tasks } = get()
      set({ 
        tasks: tasks.filter(task => task.id !== id),
        loading: false 
      })
      message.success('任务删除成功')
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '删除任务失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
      throw error
    }
  },

  pauseTask: async (id: string) => {
    try {
      await apiService.pauseTask(id)
      const { tasks, currentTask } = get()
      
      const updatedTasks = tasks.map(task => 
        task.id === id ? { ...task, status: 'paused' as const } : task
      )
      
      set({
        tasks: updatedTasks,
        currentTask: currentTask?.id === id 
          ? { ...currentTask, status: 'paused' as const } 
          : currentTask
      })
      message.success('任务已暂停')
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '暂停任务失败'
      message.error(errorMessage)
      throw error
    }
  },

  resumeTask: async (id: string) => {
    try {
      await apiService.resumeTask(id)
      const { tasks, currentTask } = get()
      
      const updatedTasks = tasks.map(task => 
        task.id === id ? { ...task, status: 'active' as const } : task
      )
      
      set({
        tasks: updatedTasks,
        currentTask: currentTask?.id === id 
          ? { ...currentTask, status: 'active' as const } 
          : currentTask
      })
      message.success('任务已恢复')
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '恢复任务失败'
      message.error(errorMessage)
      throw error
    }
  },

  fetchTaskExecutions: async (taskId: string) => {
    set({ loading: true, error: null })
    try {
      const executions = await apiService.getTaskExecutions(taskId)
      set({ executions, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取执行记录失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  getTaskStatus: async () => {
    try {
      return await apiService.getTaskStatus()
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取任务状态失败'
      message.error(errorMessage)
      throw error
    }
  },

  clearError: () => {
    set({ error: null })
  },

  clearCurrentTask: () => {
    set({ currentTask: null, executions: [] })
  },
}))
