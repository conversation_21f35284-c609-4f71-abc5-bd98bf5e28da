import { create } from 'zustand'
import { message } from 'antd'
import apiService from '@/services/api'
import type { TradingRecord, TradeRequest, BacktestRequest, PaginatedResponse } from '@/types'

interface TradingState {
  history: TradingRecord[]
  currentRecord: TradingRecord | null
  pagination: {
    total: number
    page: number
    per_page: number
    pages: number
  }
  loading: boolean
  error: string | null
  
  // Actions
  fetchHistory: (page?: number, per_page?: number) => Promise<void>
  fetchRecord: (id: string) => Promise<void>
  executeTrade: (request: TradeRequest) => Promise<TradingRecord>
  executeBacktest: (request: BacktestRequest) => Promise<TradingRecord>
  clearError: () => void
  clearCurrentRecord: () => void
}

export const useTradingStore = create<TradingState>((set, get) => ({
  history: [],
  currentRecord: null,
  pagination: {
    total: 0,
    page: 1,
    per_page: 20,
    pages: 0,
  },
  loading: false,
  error: null,

  fetchHistory: async (page = 1, per_page = 20) => {
    set({ loading: true, error: null })
    try {
      const response: PaginatedResponse<TradingRecord> = await apiService.getTradingHistory(page, per_page)
      set({ 
        history: response.items,
        pagination: {
          total: response.total,
          page: response.page,
          per_page: response.per_page,
          pages: response.pages,
        },
        loading: false 
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取交易历史失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  fetchRecord: async (id: string) => {
    set({ loading: true, error: null })
    try {
      const record = await apiService.getTradingRecord(id)
      set({ currentRecord: record, loading: false })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取交易记录失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
    }
  },

  executeTrade: async (request: TradeRequest) => {
    set({ loading: true, error: null })
    try {
      const record = await apiService.executeTrade(request)
      const { history } = get()
      set({ 
        history: [record, ...history],
        currentRecord: record,
        loading: false 
      })
      message.success('交易执行成功')
      return record
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '执行交易失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
      throw error
    }
  },

  executeBacktest: async (request: BacktestRequest) => {
    set({ loading: true, error: null })
    try {
      const record = await apiService.executeBacktest(request)
      const { history } = get()
      set({ 
        history: [record, ...history],
        currentRecord: record,
        loading: false 
      })
      message.success('回测执行成功')
      return record
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '执行回测失败'
      set({ error: errorMessage, loading: false })
      message.error(errorMessage)
      throw error
    }
  },

  clearError: () => {
    set({ error: null })
  },

  clearCurrentRecord: () => {
    set({ currentRecord: null })
  },
}))
