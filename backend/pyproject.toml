[tool.poetry]
name = "ai-fund"
version = "0.1.0"
description = "An AI-powered hedge fund that uses multiple agents to make trading decisions"
authors = ["Your Name <<EMAIL>>"]
package-mode = false
[tool.poetry.dependencies]
python = "^3.9"
langchain = "0.3.0"
langchain-anthropic = "0.3.5"
langchain-groq = "0.2.3"
langchain-openai = "^0.3.5"
langchain-deepseek = "^0.1.2"
langgraph = "0.2.56"
pandas = "^2.1.0"
numpy = "^1.24.0"
python-dotenv = "1.0.0"
matplotlib = "^3.9.2"
tabulate = "^0.9.0"
colorama = "^0.4.6"
questionary = "^2.1.0"
rich = "^13.9.4"
langchain-google-genai = "^2.0.11"
# Web API依赖
flask = "^2.3.0"
flask-cors = "^4.0.0"
flask-restx = "^1.3.0"
flask-jwt-extended = "^4.6.0"
authlib = "^1.3.0"
requests = "^2.31.0"
marshmallow = "^3.20.1"
# 数据库依赖
psycopg2-binary = "^2.9.9"
sqlalchemy = "^2.0.27"
# 任务调度依赖
apscheduler = "^3.10.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 420
target-version = ['py39']
include = '\.pyi?$'
