from flask import request
from flask_restx import Namespace, Resource, fields
from flask_jwt_extended import jwt_required
from datetime import datetime
import uuid
from task.trading_service import run_trading_task
from task.backtest_service import run_backtest_task
from data.database import get_db_connection

api = Namespace('trading', description='交易相关接口')

# 请求模型
trade_request_model = api.model('TradeRequest', {
    'tickers': fields.List(fields.String, required=True, description='股票代码列表'),
    'analysts': fields.List(fields.String, required=True, description='分析师列表'),
    'model': fields.String(required=True, description='LLM模型'),
    'trading_date': fields.String(description='交易日期'),
    'initial_cash': fields.Float(required=True, description='初始现金'),
    'margin_requirement': fields.Float(required=True, description='保证金要求'),
    'show_reasoning': fields.Boolean(required=True, description='显示推理过程')
})

backtest_request_model = api.model('BacktestRequest', {
    'tickers': fields.List(fields.String, required=True, description='股票代码列表'),
    'analysts': fields.List(fields.String, required=True, description='分析师列表'),
    'model': fields.String(required=True, description='LLM模型'),
    'start_date': fields.String(required=True, description='开始日期'),
    'end_date': fields.String(required=True, description='结束日期'),
    'initial_cash': fields.Float(required=True, description='初始现金'),
    'margin_requirement': fields.Float(required=True, description='保证金要求'),
    'show_reasoning': fields.Boolean(required=True, description='显示推理过程'),
    'force_refresh': fields.Boolean(description='强制刷新数据'),
    'use_default_period': fields.Boolean(description='使用默认时间段')
})

# 响应模型
trading_record_model = api.model('TradingRecord', {
    'id': fields.String(description='记录ID'),
    'type': fields.String(description='类型'),
    'tickers': fields.List(fields.String, description='股票代码'),
    'analysts': fields.List(fields.String, description='分析师'),
    'model': fields.String(description='模型'),
    'trading_date': fields.String(description='交易日期'),
    'initial_cash': fields.Float(description='初始现金'),
    'margin_requirement': fields.Float(description='保证金要求'),
    'show_reasoning': fields.Boolean(description='显示推理过程'),
    'status': fields.String(description='状态'),
    'created_at': fields.String(description='创建时间'),
    'completed_at': fields.String(description='完成时间'),
    'decisions': fields.Raw(description='决策结果'),
    'analyst_signals': fields.Raw(description='分析师信号'),
    'execution_log': fields.String(description='执行日志'),
    'metrics': fields.Raw(description='指标数据')
})

@api.route('/run')
class RunTrading(Resource):
    @jwt_required()
    @api.expect(trade_request_model)
    @api.marshal_with(api.model('TradingResponse', {
        'success': fields.Boolean(),
        'data': fields.Nested(trading_record_model),
        'message': fields.String()
    }))
    def post(self):
        """执行交易"""
        data = request.get_json()
        
        # 创建交易记录
        record_id = str(uuid.uuid4())
        record = {
            'id': record_id,
            'type': 'trading',
            'tickers': data['tickers'],
            'analysts': data['analysts'],
            'model': data['model'],
            'trading_date': data.get('trading_date', datetime.now().isoformat()),
            'initial_cash': data['initial_cash'],
            'margin_requirement': data['margin_requirement'],
            'show_reasoning': data['show_reasoning'],
            'status': 'running',
            'created_at': datetime.now().isoformat(),
            'completed_at': None,
            'decisions': None,
            'analyst_signals': None,
            'execution_log': None,
            'metrics': None
        }
        
        try:
            # 保存记录到数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO trading_records 
                (id, type, tickers, analysts, model, trading_date, initial_cash, 
                 margin_requirement, show_reasoning, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record['id'], record['type'], ','.join(record['tickers']),
                ','.join(record['analysts']), record['model'], record['trading_date'],
                record['initial_cash'], record['margin_requirement'], 
                record['show_reasoning'], record['status'], record['created_at']
            ))
            conn.commit()
            conn.close()
            
            # 异步执行交易逻辑
            # TODO: 实现异步任务队列
            # 这里暂时同步执行
            result = run_trading_task(
                tickers=data['tickers'],
                analysts=data['analysts'],
                model=data['model'],
                trading_date=data.get('trading_date'),
                initial_cash=data['initial_cash'],
                margin_requirement=data['margin_requirement'],
                show_reasoning=data['show_reasoning']
            )
            
            # 更新记录状态
            record['status'] = 'completed'
            record['completed_at'] = datetime.now().isoformat()
            record['decisions'] = result.get('decisions')
            record['analyst_signals'] = result.get('analyst_signals')
            record['execution_log'] = result.get('execution_log')
            record['metrics'] = result.get('metrics')
            
            # 更新数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE trading_records 
                SET status=?, completed_at=?, decisions=?, analyst_signals=?, 
                    execution_log=?, metrics=?
                WHERE id=?
            """, (
                record['status'], record['completed_at'], 
                str(record['decisions']), str(record['analyst_signals']),
                record['execution_log'], str(record['metrics']), record['id']
            ))
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'data': record,
                'message': '交易执行成功'
            }
            
        except Exception as e:
            # 更新记录状态为失败
            record['status'] = 'failed'
            record['execution_log'] = str(e)
            
            try:
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE trading_records 
                    SET status=?, execution_log=?
                    WHERE id=?
                """, (record['status'], record['execution_log'], record['id']))
                conn.commit()
                conn.close()
            except:
                pass
            
            return {
                'success': False,
                'message': f'交易执行失败: {str(e)}'
            }, 500

@api.route('/backtest')
class RunBacktest(Resource):
    @jwt_required()
    @api.expect(backtest_request_model)
    @api.marshal_with(api.model('BacktestResponse', {
        'success': fields.Boolean(),
        'data': fields.Nested(trading_record_model),
        'message': fields.String()
    }))
    def post(self):
        """执行回测"""
        data = request.get_json()
        
        # 创建回测记录
        record_id = str(uuid.uuid4())
        record = {
            'id': record_id,
            'type': 'backtest',
            'tickers': data['tickers'],
            'analysts': data['analysts'],
            'model': data['model'],
            'trading_date': f"{data['start_date']} to {data['end_date']}",
            'initial_cash': data['initial_cash'],
            'margin_requirement': data['margin_requirement'],
            'show_reasoning': data['show_reasoning'],
            'status': 'running',
            'created_at': datetime.now().isoformat(),
            'completed_at': None,
            'decisions': None,
            'analyst_signals': None,
            'execution_log': None,
            'metrics': None
        }
        
        try:
            # 保存记录到数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO trading_records 
                (id, type, tickers, analysts, model, trading_date, initial_cash, 
                 margin_requirement, show_reasoning, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record['id'], record['type'], ','.join(record['tickers']),
                ','.join(record['analysts']), record['model'], record['trading_date'],
                record['initial_cash'], record['margin_requirement'], 
                record['show_reasoning'], record['status'], record['created_at']
            ))
            conn.commit()
            conn.close()
            
            # 执行回测逻辑
            result = run_backtest_task(
                tickers=data['tickers'],
                analysts=data['analysts'],
                model=data['model'],
                start_date=data['start_date'],
                end_date=data['end_date'],
                initial_cash=data['initial_cash'],
                margin_requirement=data['margin_requirement'],
                show_reasoning=data['show_reasoning'],
                force_refresh=data.get('force_refresh', False),
                use_default_period=data.get('use_default_period', False)
            )
            
            # 更新记录状态
            record['status'] = 'completed'
            record['completed_at'] = datetime.now().isoformat()
            record['decisions'] = result.get('decisions')
            record['analyst_signals'] = result.get('analyst_signals')
            record['execution_log'] = result.get('execution_log')
            record['metrics'] = result.get('metrics')
            
            # 更新数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE trading_records 
                SET status=?, completed_at=?, decisions=?, analyst_signals=?, 
                    execution_log=?, metrics=?
                WHERE id=?
            """, (
                record['status'], record['completed_at'], 
                str(record['decisions']), str(record['analyst_signals']),
                record['execution_log'], str(record['metrics']), record['id']
            ))
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'data': record,
                'message': '回测执行成功'
            }
            
        except Exception as e:
            # 更新记录状态为失败
            record['status'] = 'failed'
            record['execution_log'] = str(e)
            
            try:
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE trading_records 
                    SET status=?, execution_log=?
                    WHERE id=?
                """, (record['status'], record['execution_log'], record['id']))
                conn.commit()
                conn.close()
            except:
                pass
            
            return {
                'success': False,
                'message': f'回测执行失败: {str(e)}'
            }, 500

@api.route('/history')
class TradingHistory(Resource):
    @jwt_required()
    @api.marshal_with(api.model('HistoryResponse', {
        'success': fields.Boolean(),
        'data': fields.Nested(api.model('PaginatedRecords', {
            'items': fields.List(fields.Nested(trading_record_model)),
            'total': fields.Integer(),
            'page': fields.Integer(),
            'per_page': fields.Integer(),
            'pages': fields.Integer()
        })),
        'message': fields.String()
    }))
    def get(self):
        """获取交易历史"""
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        offset = (page - 1) * per_page
        
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取总数
            cursor.execute("SELECT COUNT(*) FROM trading_records")
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            cursor.execute("""
                SELECT * FROM trading_records 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            """, (per_page, offset))
            
            records = []
            for row in cursor.fetchall():
                record = {
                    'id': row[0],
                    'type': row[1],
                    'tickers': row[2].split(',') if row[2] else [],
                    'analysts': row[3].split(',') if row[3] else [],
                    'model': row[4],
                    'trading_date': row[5],
                    'initial_cash': row[6],
                    'margin_requirement': row[7],
                    'show_reasoning': bool(row[8]),
                    'status': row[9],
                    'created_at': row[10],
                    'completed_at': row[11],
                    'decisions': row[12],
                    'analyst_signals': row[13],
                    'execution_log': row[14],
                    'metrics': row[15]
                }
                records.append(record)
            
            conn.close()
            
            pages = (total + per_page - 1) // per_page
            
            return {
                'success': True,
                'data': {
                    'items': records,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': pages
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取历史记录失败: {str(e)}'
            }, 500

@api.route('/records/<string:record_id>')
class TradingRecord(Resource):
    @jwt_required()
    @api.marshal_with(api.model('RecordResponse', {
        'success': fields.Boolean(),
        'data': fields.Nested(trading_record_model),
        'message': fields.String()
    }))
    def get(self, record_id):
        """获取交易记录详情"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM trading_records WHERE id=?", (record_id,))
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return {
                    'success': False,
                    'message': '记录不存在'
                }, 404
            
            record = {
                'id': row[0],
                'type': row[1],
                'tickers': row[2].split(',') if row[2] else [],
                'analysts': row[3].split(',') if row[3] else [],
                'model': row[4],
                'trading_date': row[5],
                'initial_cash': row[6],
                'margin_requirement': row[7],
                'show_reasoning': bool(row[8]),
                'status': row[9],
                'created_at': row[10],
                'completed_at': row[11],
                'decisions': row[12],
                'analyst_signals': row[13],
                'execution_log': row[14],
                'metrics': row[15]
            }
            
            return {
                'success': True,
                'data': record
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'获取记录失败: {str(e)}'
            }, 500
