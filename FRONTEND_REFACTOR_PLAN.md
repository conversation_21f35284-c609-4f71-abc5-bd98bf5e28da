# AI Fund 前后端分离重构计划

## 项目概述

将现有的Flask单体应用重构为前后端分离的架构：
- **后端**: Flask API服务，提供RESTful接口
- **前端**: TypeScript + React + Vite，现代化单页应用

## 现有前端代码分析

### 页面结构
1. **首页 (index.html)**: 交易和回测表单，包含复杂的表单验证和动态交互
2. **历史记录 (history.html)**: 分页表格，筛选功能，Bootstrap标签页
3. **任务队列 (task_queue.html)**: 任务管理，实时状态更新
4. **任务详情 (task_detail.html)**: 图表展示，实时数据更新
5. **回测结果 (backtest_results.html)**: 复杂图表，数据可视化

### JavaScript功能模块
1. **index.js**: 表单处理，Select2集成，表单验证，本地存储
2. **history.js**: 分页逻辑，筛选功能，Bootstrap组件初始化
3. **task_queue.js**: 实时任务状态监控，AJAX操作，自动刷新
4. **task_detail.js**: Chart.js图表，实时数据更新，时间筛选
5. **backtest_chart.js**: 复杂图表系统，多股票切换，分析师信号展示

### 依赖关系
- **Bootstrap 5**: UI框架
- **Chart.js**: 图表库
- **Select2**: 下拉选择组件
- **jQuery**: DOM操作和AJAX

## 新架构设计

### 项目结构
```
AIFund/
├── backend/                 # Flask API服务
│   ├── src/                # 现有后端代码
│   ├── requirements.txt    # Python依赖
│   └── Dockerfile         # 后端容器配置
├── frontend/              # TypeScript前端项目
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── pages/        # 页面组件
│   │   ├── services/     # API服务
│   │   ├── hooks/        # 自定义Hooks
│   │   ├── types/        # TypeScript类型定义
│   │   ├── utils/        # 工具函数
│   │   └── stores/       # 状态管理
│   ├── public/           # 静态资源
│   ├── package.json      # 前端依赖
│   ├── vite.config.ts    # Vite配置
│   └── Dockerfile        # 前端容器配置
└── docker-compose.yml     # 容器编排
```

### 技术栈选择

#### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI库**: Ant Design (替代Bootstrap，更好的React集成)
- **图表库**: Apache ECharts (替代Chart.js，更强大的功能)
- **状态管理**: Zustand (轻量级状态管理)
- **HTTP客户端**: Axios
- **路由**: React Router v6
- **表单处理**: React Hook Form + Zod验证
- **样式**: Tailwind CSS + Ant Design

#### 后端API设计
- **框架**: Flask + Flask-RESTX (API文档生成)
- **CORS**: Flask-CORS
- **认证**: JWT Token
- **API文档**: Swagger/OpenAPI

### API接口设计

#### 认证相关
```
POST /api/auth/login          # 用户登录
POST /api/auth/logout         # 用户登出
GET  /api/auth/me            # 获取当前用户信息
```

#### 交易相关
```
POST /api/trading/run         # 执行交易
POST /api/trading/backtest    # 执行回测
GET  /api/trading/history     # 获取交易历史
GET  /api/trading/records/{id} # 获取交易记录详情
```

#### 任务管理
```
GET    /api/tasks             # 获取任务列表
POST   /api/tasks             # 创建任务
GET    /api/tasks/{id}        # 获取任务详情
PUT    /api/tasks/{id}        # 更新任务
DELETE /api/tasks/{id}        # 删除任务
POST   /api/tasks/{id}/pause  # 暂停任务
POST   /api/tasks/{id}/resume # 恢复任务
GET    /api/tasks/status      # 获取任务状态统计
```

#### 数据相关
```
GET /api/data/stocks          # 获取股票列表
GET /api/data/prices/{ticker} # 获取股票价格数据
GET /api/data/refresh         # 刷新数据
GET /api/data/monitor         # 数据监控状态
```

#### 配置相关
```
GET /api/config/analysts      # 获取分析师列表
GET /api/config/models        # 获取模型列表
GET /api/config/stocks        # 获取股票配置
```

### 前端页面组件设计

#### 1. 布局组件
- **AppLayout**: 主布局，包含导航栏和侧边栏
- **Navbar**: 顶部导航栏
- **Sidebar**: 侧边栏导航

#### 2. 页面组件
- **HomePage**: 首页，交易和回测表单
- **HistoryPage**: 历史记录页面
- **TaskQueuePage**: 任务队列页面
- **TaskDetailPage**: 任务详情页面
- **DataMonitorPage**: 数据监控页面

#### 3. 业务组件
- **TradingForm**: 交易表单组件
- **BacktestForm**: 回测表单组件
- **TaskTable**: 任务表格组件
- **StockChart**: 股票图表组件
- **PortfolioChart**: 投资组合图表组件
- **AnalystSignals**: 分析师信号组件

#### 4. 通用组件
- **LoadingSpinner**: 加载动画
- **ErrorBoundary**: 错误边界
- **ConfirmModal**: 确认对话框
- **DateRangePicker**: 日期范围选择器

### 状态管理设计

使用Zustand进行状态管理，按功能模块划分：

#### 1. 用户状态 (useUserStore)
```typescript
interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  fetchUser: () => Promise<void>;
}
```

#### 2. 任务状态 (useTaskStore)
```typescript
interface TaskState {
  tasks: Task[];
  currentTask: Task | null;
  loading: boolean;
  fetchTasks: () => Promise<void>;
  createTask: (task: CreateTaskRequest) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}
```

#### 3. 交易状态 (useTradingStore)
```typescript
interface TradingState {
  history: TradingRecord[];
  currentRecord: TradingRecord | null;
  loading: boolean;
  fetchHistory: () => Promise<void>;
  executeTrade: (request: TradeRequest) => Promise<void>;
  executeBacktest: (request: BacktestRequest) => Promise<void>;
}
```

#### 4. 数据状态 (useDataStore)
```typescript
interface DataState {
  stocks: Stock[];
  prices: Record<string, PriceData[]>;
  analysts: Analyst[];
  models: Model[];
  loading: boolean;
  fetchStocks: () => Promise<void>;
  fetchPrices: (ticker: string) => Promise<void>;
}
```

### 开发和部署配置

#### 开发环境
- **前端开发服务器**: Vite dev server (http://localhost:3000)
- **后端开发服务器**: Flask dev server (http://localhost:8888)
- **代理配置**: Vite代理API请求到后端

#### 生产环境
- **前端**: 静态文件部署到Nginx
- **后端**: Flask应用部署到容器
- **反向代理**: Nginx处理前端静态文件和API代理

#### Docker配置
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
  
  backend:
    build: ./backend
    ports:
      - "8888:8888"
    environment:
      - FLASK_ENV=production
    depends_on:
      - postgres
  
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=aifund
      - POSTGRES_USER=aifund
      - POSTGRES_PASSWORD=password
```

## 迁移策略

### 阶段1: 后端API化
1. 保留现有Flask应用
2. 添加API路由和CORS支持
3. 重构现有路由为API接口
4. 添加JWT认证

### 阶段2: 前端项目创建
1. 创建React + TypeScript项目
2. 配置开发环境和构建工具
3. 实现基础布局和路由

### 阶段3: 核心功能迁移
1. 实现用户认证和状态管理
2. 迁移交易表单和历史记录页面
3. 实现任务管理功能

### 阶段4: 高级功能迁移
1. 迁移图表和数据可视化
2. 实现实时数据更新
3. 优化性能和用户体验

### 阶段5: 测试和部署
1. 端到端测试
2. 性能优化
3. 生产环境部署

## 预期收益

1. **开发效率**: 前后端独立开发，提高开发效率
2. **用户体验**: 单页应用，更流畅的用户交互
3. **可维护性**: 代码结构清晰，类型安全
4. **可扩展性**: 模块化设计，易于扩展新功能
5. **性能**: 现代前端技术栈，更好的性能表现
